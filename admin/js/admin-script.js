/**
 * WP Favorites Admin JavaScript
 */

(function($) {
    'use strict';

    // Main admin object
    window.WPFavoritesAdmin = {
        
        // Initialize
        init: function() {
            this.bindEvents();
            this.initColorPickers();
            this.updatePreview();
        },
        
        // Bind events
        bindEvents: function() {
            // Icon upload
            $(document).on('click', '.wp-favorites-upload-icon', this.uploadIcon);
            
            // Remove icon
            $(document).on('click', '.wp-favorites-remove-icon', this.removeIcon);
            
            // Settings change preview
            $(document).on('change', '#wp_favorites_icon_position, #wp_favorites_icon_size, #wp_favorites_icon_color, #wp_favorites_icon_color_active', this.updatePreview);
            
            // Tab switching
            $(document).on('click', '.nav-tab', this.switchTab);
            
            // Form validation
            $(document).on('submit', 'form', this.validateForm);
        },
        
        // Initialize color pickers
        initColorPickers: function() {
            if ($.fn.wpColorPicker) {
                $('#wp_favorites_icon_color, #wp_favorites_icon_color_active').wpColorPicker({
                    change: function() {
                        WPFavoritesAdmin.updatePreview();
                    }
                });
            }
        },
        
        // Upload icon
        uploadIcon: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var frame;
            
            // Create media frame
            frame = wp.media({
                title: wpFavoritesAdmin.strings.selectIcon,
                button: {
                    text: wpFavoritesAdmin.strings.useIcon
                },
                library: {
                    type: 'image'
                },
                multiple: false
            });
            
            // Handle selection
            frame.on('select', function() {
                var attachment = frame.state().get('selection').first().toJSON();
                
                // Validate file type
                if (!WPFavoritesAdmin.isValidIconFile(attachment)) {
                    alert('Please select a valid image file (SVG, PNG, JPG).');
                    return;
                }
                
                // Update preview
                WPFavoritesAdmin.updateIconPreview(attachment);
                
                // Save to database
                WPFavoritesAdmin.saveIcon(attachment.id);
            });
            
            // Open frame
            frame.open();
        },
        
        // Remove icon
        removeIcon: function(e) {
            e.preventDefault();
            
            if (!confirm(wpFavoritesAdmin.strings.confirmRemove)) {
                return;
            }
            
            var $button = $(this);
            var $field = $button.closest('.wp-favorites-custom-icon-field');
            
            // Clear preview
            $field.find('.icon-preview').html('<div class="no-icon">No custom icon selected</div>');
            
            // Clear hidden field
            $field.find('#wp_favorites_custom_icon_id').val('0');
            
            // Hide remove button
            $button.hide();
            
            // Update preview
            WPFavoritesAdmin.updatePreview();
            
            // Show success message
            WPFavoritesAdmin.showMessage('Icon removed successfully.', 'success');
        },
        
        // Validate icon file
        isValidIconFile: function(attachment) {
            var validTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];
            return validTypes.includes(attachment.mime);
        },
        
        // Update icon preview
        updateIconPreview: function(attachment) {
            var $field = $('.wp-favorites-custom-icon-field');
            var $preview = $field.find('.icon-preview');
            var $hiddenField = $field.find('#wp_favorites_custom_icon_id');
            var $removeButton = $field.find('.wp-favorites-remove-icon');
            
            // Update preview image
            $preview.html('<img src="' + attachment.url + '" alt="Custom Icon">');
            
            // Update hidden field
            $hiddenField.val(attachment.id);
            
            // Show remove button
            $removeButton.show();
            
            // Update live preview
            WPFavoritesAdmin.updatePreview();
        },
        
        // Save icon to database
        saveIcon: function(attachmentId) {
            $.ajax({
                url: wpFavoritesAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_upload_icon',
                    attachment_id: attachmentId,
                    nonce: wpFavoritesAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WPFavoritesAdmin.showMessage(response.data.message, 'success');
                    } else {
                        WPFavoritesAdmin.showMessage(response.data.message || 'Error uploading icon.', 'error');
                    }
                },
                error: function() {
                    WPFavoritesAdmin.showMessage('Error uploading icon.', 'error');
                }
            });
        },
        
        // Update preview
        updatePreview: function() {
            var position = $('#wp_favorites_icon_position').val() || 'top-right';
            var size = $('#wp_favorites_icon_size').val() || 'medium';
            var color = $('#wp_favorites_icon_color').val() || '#666666';
            var colorActive = $('#wp_favorites_icon_color_active').val() || '#e74c3c';
            var customIconId = $('#wp_favorites_custom_icon_id').val() || '0';
            
            // Create preview if it doesn't exist
            if ($('.wp-favorites-preview').length === 0) {
                WPFavoritesAdmin.createPreview();
            }
            
            var $preview = $('.wp-favorites-preview');
            var $button = $preview.find('.preview-favorites-button');
            
            // Update position
            $button.removeClass('position-top-left position-top-right position-bottom-left position-bottom-right');
            $button.addClass('position-' + position);
            
            // Update size
            var sizes = {
                'small': '30px',
                'medium': '40px',
                'large': '50px'
            };
            var iconSize = sizes[size] || sizes['medium'];
            var iconInnerSize = (parseInt(iconSize) * 0.5) + 'px';
            
            $button.css({
                'width': iconSize,
                'height': iconSize
            });
            
            // Update icon
            var iconHtml;
            if (customIconId !== '0') {
                var customIconUrl = $('.icon-preview img').attr('src');
                if (customIconUrl) {
                    iconHtml = '<img src="' + customIconUrl + '" style="width: ' + iconInnerSize + '; height: ' + iconInnerSize + ';">';
                } else {
                    iconHtml = WPFavoritesAdmin.getDefaultIcon(color, iconInnerSize);
                }
            } else {
                iconHtml = WPFavoritesAdmin.getDefaultIcon(color, iconInnerSize);
            }
            
            $button.html(iconHtml);
            
            // Update position styles
            WPFavoritesAdmin.updatePreviewPosition(position);
        },
        
        // Create preview section
        createPreview: function() {
            var previewHtml = '<div class="wp-favorites-preview">' +
                '<h3>Preview</h3>' +
                '<div class="preview-item">' +
                '<img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlbTwvdGV4dD48L3N2Zz4=" alt="Product Preview">' +
                '<button class="preview-favorites-button position-top-right">' +
                WPFavoritesAdmin.getDefaultIcon('#666666', '20px') +
                '</button>' +
                '<div style="padding: 10px; text-align: left;">' +
                '<h4 style="margin: 0 0 5px 0; font-size: 14px;">Product Name</h4>' +
                '<div style="color: #e74c3c; font-weight: bold;">€29.99</div>' +
                '</div>' +
                '</div>' +
                '</div>';
            
            $('.form-table').last().after(previewHtml);
        },
        
        // Get default heart icon
        getDefaultIcon: function(color, size) {
            return '<svg style="width: ' + size + '; height: ' + size + '; stroke: ' + color + '; fill: none; stroke-width: 2;" viewBox="0 0 24 24">' +
                '<path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>' +
                '</svg>';
        },
        
        // Update preview position
        updatePreviewPosition: function(position) {
            var $button = $('.preview-favorites-button');
            
            // Reset all positions
            $button.css({
                'top': 'auto',
                'right': 'auto',
                'bottom': 'auto',
                'left': 'auto'
            });
            
            // Apply new position
            switch (position) {
                case 'top-left':
                    $button.css({ 'top': '10px', 'left': '10px' });
                    break;
                case 'top-right':
                    $button.css({ 'top': '10px', 'right': '10px' });
                    break;
                case 'bottom-left':
                    $button.css({ 'bottom': '10px', 'left': '10px' });
                    break;
                case 'bottom-right':
                    $button.css({ 'bottom': '10px', 'right': '10px' });
                    break;
            }
        },
        
        // Switch tabs
        switchTab: function(e) {
            e.preventDefault();
            
            var $tab = $(this);
            var tabId = $tab.attr('href').split('tab=')[1];
            
            // Update active tab
            $('.nav-tab').removeClass('nav-tab-active');
            $tab.addClass('nav-tab-active');
            
            // Update URL without reload
            if (history.pushState) {
                var newUrl = window.location.href.split('&tab=')[0] + '&tab=' + tabId;
                history.pushState(null, null, newUrl);
            }
        },
        
        // Validate form
        validateForm: function(e) {
            var isValid = true;
            var errors = [];
            
            // Validate favorites page selection
            var pageId = $('#wp_favorites_favorites_page_id').val();
            if (!pageId || pageId === '0') {
                errors.push('Please select a page for displaying favorites.');
                isValid = false;
            }
            
            // Show errors if any
            if (!isValid) {
                e.preventDefault();
                WPFavoritesAdmin.showMessage(errors.join('<br>'), 'error');
            }
            
            return isValid;
        },
        
        // Show message
        showMessage: function(message, type) {
            type = type || 'info';
            
            // Remove existing messages
            $('.wp-favorites-admin-message').remove();
            
            // Create message
            var $message = $('<div class="notice notice-' + type + ' wp-favorites-admin-message is-dismissible"><p>' + message + '</p></div>');
            
            // Add to page
            $('.wrap h1').after($message);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $message.fadeOut(300, function() {
                    $message.remove();
                });
            }, 5000);
            
            // Scroll to message
            $('html, body').animate({
                scrollTop: $message.offset().top - 50
            }, 300);
        },
        
        // Show loading state
        showLoading: function($element) {
            $element.addClass('wp-favorites-loading');
        },
        
        // Hide loading state
        hideLoading: function($element) {
            $element.removeClass('wp-favorites-loading');
        }
    };

    // Translations functionality
    window.WPFavoritesTranslations = {

        // Initialize translations
        init: function() {
            this.bindEvents();
        },

        // Bind translation events
        bindEvents: function() {
            // Save translation
            $(document).on('click', '.save-translation', this.saveTranslation);

            // Delete translation
            $(document).on('click', '.delete-translation', this.deleteTranslation);

            // Export translations
            $(document).on('click', '#export-translations', this.exportTranslations);

            // Import translations
            $(document).on('click', '#import-translations', this.importTranslations);
            $(document).on('change', '#import-file', this.handleImportFile);

            // Auto-save on textarea blur
            $(document).on('blur', '.translation-textarea', this.autoSave);
        },

        // Save translation
        saveTranslation: function(e) {
            e.preventDefault();

            var $button = $(this);
            var $row = $button.closest('.translation-row');
            var $textarea = $row.find('.translation-textarea');

            var originalText = $textarea.data('original');
            var translatedText = $textarea.val();
            var languageCode = $textarea.data('language');

            if (!translatedText.trim()) {
                alert(wpFavoritesAdmin.strings.translationRequired || 'Translation is required.');
                return;
            }

            // Show loading state
            $row.addClass('saving');
            $button.prop('disabled', true);

            $.ajax({
                url: wpFavoritesAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_save_translation',
                    nonce: wpFavoritesAdmin.nonce,
                    original_text: originalText,
                    translated_text: translatedText,
                    language_code: languageCode,
                    context: ''
                },
                success: function(response) {
                    if (response.success) {
                        $row.removeClass('untranslated').addClass('translated success');

                        // Add delete button if not exists
                        if (!$row.find('.delete-translation').length) {
                            $button.after('<button type="button" class="button delete-translation">Delete</button>');
                        }

                        setTimeout(function() {
                            $row.removeClass('success');
                        }, 2000);
                    } else {
                        $row.addClass('error');
                        alert(response.data || 'Failed to save translation.');
                        setTimeout(function() {
                            $row.removeClass('error');
                        }, 2000);
                    }
                },
                error: function() {
                    $row.addClass('error');
                    alert('An error occurred while saving the translation.');
                    setTimeout(function() {
                        $row.removeClass('error');
                    }, 2000);
                },
                complete: function() {
                    $row.removeClass('saving');
                    $button.prop('disabled', false);
                }
            });
        },

        // Delete translation
        deleteTranslation: function(e) {
            e.preventDefault();

            if (!confirm(wpFavoritesAdmin.strings.confirmDelete || 'Are you sure you want to delete this translation?')) {
                return;
            }

            var $button = $(this);
            var $row = $button.closest('.translation-row');
            var $textarea = $row.find('.translation-textarea');

            var stringKey = $row.data('string-key');
            var languageCode = $textarea.data('language');

            // Show loading state
            $row.addClass('saving');
            $button.prop('disabled', true);

            $.ajax({
                url: wpFavoritesAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_delete_translation',
                    nonce: wpFavoritesAdmin.nonce,
                    string_key: stringKey,
                    language_code: languageCode,
                    context: ''
                },
                success: function(response) {
                    if (response.success) {
                        $textarea.val('');
                        $row.removeClass('translated').addClass('untranslated success');
                        $button.remove();

                        setTimeout(function() {
                            $row.removeClass('success');
                        }, 2000);
                    } else {
                        $row.addClass('error');
                        alert(response.data || 'Failed to delete translation.');
                        setTimeout(function() {
                            $row.removeClass('error');
                        }, 2000);
                    }
                },
                error: function() {
                    $row.addClass('error');
                    alert('An error occurred while deleting the translation.');
                    setTimeout(function() {
                        $row.removeClass('error');
                    }, 2000);
                },
                complete: function() {
                    $row.removeClass('saving');
                    $button.prop('disabled', false);
                }
            });
        },

        // Auto-save translation
        autoSave: function() {
            var $textarea = $(this);
            var $row = $textarea.closest('.translation-row');
            var $saveButton = $row.find('.save-translation');

            if ($textarea.val().trim() && $textarea.data('original-value') !== $textarea.val()) {
                $saveButton.trigger('click');
            }

            $textarea.data('original-value', $textarea.val());
        },

        // Export translations
        exportTranslations: function(e) {
            e.preventDefault();

            var languageCode = $('#translation-language').val();

            $.ajax({
                url: wpFavoritesAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_export_translations',
                    nonce: wpFavoritesAdmin.nonce,
                    language_code: languageCode
                },
                success: function(response) {
                    if (response.success) {
                        var dataStr = JSON.stringify(response.data.data, null, 2);
                        var dataBlob = new Blob([dataStr], {type: 'application/json'});

                        var link = document.createElement('a');
                        link.href = URL.createObjectURL(dataBlob);
                        link.download = response.data.filename;
                        link.click();
                    } else {
                        alert(response.data || 'Failed to export translations.');
                    }
                },
                error: function() {
                    alert('An error occurred while exporting translations.');
                }
            });
        },

        // Import translations
        importTranslations: function(e) {
            e.preventDefault();
            $('#import-file').click();
        },

        // Handle import file
        handleImportFile: function(e) {
            var file = e.target.files[0];
            if (!file) return;

            var reader = new FileReader();
            reader.onload = function(e) {
                try {
                    var importData = JSON.parse(e.target.result);
                    WPFavoritesTranslations.processImport(importData);
                } catch (error) {
                    alert('Invalid JSON file format.');
                }
            };
            reader.readAsText(file);
        },

        // Process import data
        processImport: function(importData) {
            $.ajax({
                url: wpFavoritesAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_import_translations',
                    nonce: wpFavoritesAdmin.nonce,
                    import_data: JSON.stringify(importData)
                },
                success: function(response) {
                    if (response.success) {
                        alert(response.data.message);
                        location.reload(); // Reload to show imported translations
                    } else {
                        alert(response.data || 'Failed to import translations.');
                    }
                },
                error: function() {
                    alert('An error occurred while importing translations.');
                }
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        WPFavoritesAdmin.init();
        WPFavoritesTranslations.init();
    });

})(jQuery);
