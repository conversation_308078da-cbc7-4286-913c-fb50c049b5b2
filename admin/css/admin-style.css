/**
 * WP Favorites Admin Styles
 */

/* Settings Page Layout */
.wp-favorites-settings {
    max-width: 1200px;
}

/* Tab Navigation */
.nav-tab-wrapper {
    margin-bottom: 20px;
}

/* Form Tables */
.form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
}

.form-table td {
    padding: 15px 10px;
}

/* Color Fields */
.wp-favorites-color-fields {
    display: flex;
    gap: 20px;
    align-items: center;
}

.color-field {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-field label {
    font-weight: 600;
    min-width: 100px;
}

.color-field input[type="color"] {
    width: 50px;
    height: 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

/* Custom Icon Field */
.wp-favorites-custom-icon-field {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.icon-preview {
    width: 60px;
    height: 60px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9f9f9;
}

.icon-preview img {
    max-width: 50px;
    max-height: 50px;
    object-fit: contain;
}

.no-icon {
    color: #666;
    font-size: 12px;
    text-align: center;
    line-height: 1.2;
}

.wp-favorites-upload-icon,
.wp-favorites-remove-icon {
    margin-right: 10px;
}

.wp-favorites-remove-icon {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
}

.wp-favorites-remove-icon:hover {
    background: #c82333;
    border-color: #bd2130;
    color: white;
}

/* Grid Columns Field */
.wp-favorites-grid-columns {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.column-field {
    display: flex;
    align-items: center;
    gap: 8px;
}

.column-field label {
    font-weight: 600;
    min-width: 70px;
}

.column-field select {
    min-width: 60px;
}

/* Settings Sections */
.form-table {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin-bottom: 20px;
}

.form-table th {
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.form-table td {
    border-bottom: 1px solid #e0e0e0;
}

.form-table tr:last-child th,
.form-table tr:last-child td {
    border-bottom: none;
}

/* Description Text */
.description {
    color: #666;
    font-style: italic;
    margin-top: 5px;
}

/* Settings Header */
.wp-favorites-settings-header {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.wp-favorites-settings-header h2 {
    margin: 0 0 10px 0;
    color: #333;
}

.wp-favorites-settings-header p {
    margin: 0;
    color: #666;
    line-height: 1.6;
}

/* Preview Section */
.wp-favorites-preview {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.wp-favorites-preview h3 {
    margin: 0 0 15px 0;
    color: #333;
}

.preview-item {
    position: relative;
    display: inline-block;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    margin-right: 15px;
    width: 200px;
    text-align: center;
}

.preview-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 4px;
    background: #ddd;
}

.preview-favorites-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-favorites-button svg {
    width: 20px;
    height: 20px;
}

/* Notices */
.notice.wp-favorites-notice {
    border-left-color: #e74c3c;
}

.notice.wp-favorites-notice.notice-success {
    border-left-color: #27ae60;
}

/* Loading States */
.wp-favorites-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.wp-favorites-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: adminSpinner 1s linear infinite;
}

@keyframes adminSpinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Media Upload Modal Customizations */
.media-modal .wp-favorites-icon-selection {
    padding: 20px;
}

.media-modal .wp-favorites-icon-selection h3 {
    margin: 0 0 15px 0;
}

.media-modal .wp-favorites-icon-selection p {
    margin: 0 0 15px 0;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wp-favorites-color-fields {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .wp-favorites-grid-columns {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .wp-favorites-custom-icon-field {
        flex-direction: column;
        align-items: flex-start;
    }

    .form-table th,
    .form-table td {
        display: block;
        width: 100%;
        padding: 10px;
    }

    .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }

    .form-table td {
        padding-top: 5px;
    }

    /* Translations responsive styles */
    .translations-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .translation-actions {
        justify-content: center;
    }

    .translations-table-container {
        overflow-x: auto;
    }

    .translation-textarea {
        min-height: 80px;
    }

    .translation-actions .button {
        display: block;
        width: 100%;
        margin: 2px 0;
    }
}

/* Help Text */
.wp-favorites-help {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;
}

.wp-favorites-help h4 {
    margin: 0 0 10px 0;
    color: #0073aa;
}

.wp-favorites-help p {
    margin: 0 0 10px 0;
    line-height: 1.6;
}

.wp-favorites-help p:last-child {
    margin-bottom: 0;
}

.wp-favorites-help code {
    background: rgba(0, 115, 170, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: Consolas, Monaco, monospace;
}

/* Status Indicators */
.wp-favorites-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.wp-favorites-status.active {
    background: #d4edda;
    color: #155724;
}

.wp-favorites-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.wp-favorites-status-icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.wp-favorites-status.active .wp-favorites-status-icon {
    background: #28a745;
}

.wp-favorites-status.inactive .wp-favorites-status-icon {
    background: #dc3545;
}

/* Settings Tabs Content */
.nav-tab-wrapper + form {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 0;
}

.nav-tab-wrapper + form .form-table:last-of-type {
    margin-bottom: 0;
}

.nav-tab-wrapper + form .submit {
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    margin: 0;
    padding: 20px;
}

/* Custom Checkbox Styles */
.wp-favorites-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
}

.wp-favorites-checkbox input[type="checkbox"] {
    margin: 0;
}

.wp-favorites-checkbox label {
    margin: 0;
    font-weight: normal;
}

/* Settings Success Message */
.settings-error.updated {
    border-left-color: #27ae60;
}

/* Icon Upload Progress */
.wp-favorites-upload-progress {
    display: none;
    margin-top: 10px;
}

.wp-favorites-upload-progress.active {
    display: block;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #007cba;
    width: 0%;
    transition: width 0.3s ease;
}

/* Shortcode Display */
.wp-favorites-shortcode {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 10px 15px;
    font-family: Consolas, Monaco, monospace;
    font-size: 14px;
    color: #333;
    margin: 10px 0;
    display: inline-block;
}

.wp-favorites-shortcode::before {
    content: "💡 ";
    margin-right: 5px;
}

/* Translations Tab Styles */
.wp-favorites-translations-tab {
    margin-top: 20px;
}

.translations-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.language-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.language-selector label {
    font-weight: 600;
}

.language-selector select {
    min-width: 200px;
}

.translation-actions {
    display: flex;
    gap: 10px;
}

.translations-stats {
    margin-bottom: 15px;
    padding: 10px;
    background: #e7f3ff;
    border-left: 4px solid #0073aa;
}

.translations-stats p {
    margin: 0;
    font-weight: 600;
}

.translations-table-container {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.translation-row.translated {
    background-color: #f0f8f0;
}

.translation-row.untranslated {
    background-color: #fff8f0;
}

.original-text .text-content {
    font-family: monospace;
    font-size: 13px;
    line-height: 1.4;
    padding: 5px;
    background: #f5f5f5;
    border-radius: 3px;
    word-break: break-word;
}

.translation-textarea {
    width: 100%;
    min-height: 60px;
    resize: vertical;
    font-family: inherit;
    font-size: 13px;
    line-height: 1.4;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.translation-textarea:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.translation-actions {
    text-align: center;
    white-space: nowrap;
}

.translation-actions .button {
    margin: 2px;
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.4;
}

.save-translation {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

.save-translation:hover {
    background: #005a87;
    border-color: #005a87;
}

.delete-translation {
    background: #dc3232;
    border-color: #dc3232;
    color: #fff;
}

.delete-translation:hover {
    background: #a00;
    border-color: #a00;
}

/* Loading states */
.translation-row.saving {
    opacity: 0.6;
    pointer-events: none;
}

.translation-row.saving .save-translation::after {
    content: " ...";
}

/* Success/Error states */
.translation-row.success {
    background-color: #d4edda;
    transition: background-color 0.3s ease;
}

.translation-row.error {
    background-color: #f8d7da;
    transition: background-color 0.3s ease;
}
