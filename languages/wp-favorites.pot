# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: WP Favorites 1.0.0\n"
"Report-Msgid-Bugs-To: https://pixelhunter.pt\n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"

#: wp-favorites.php:45
msgid "WP Favorites requires WooCommerce to be installed and active."
msgstr ""

#: wp-favorites.php:46
msgid "Plugin Activation Error"
msgstr ""

#: wp-favorites.php:108
#, php-format
msgid "WP Favorites requires %s to be installed and active."
msgstr ""

#: includes/class-favorites-core.php:65
msgid "Remove from favorites"
msgstr ""

#: includes/class-favorites-core.php:65
msgid "Add to favorites"
msgstr ""

#: includes/class-favorites-core.php:85
msgid "Favorite"
msgstr ""

#: includes/class-favorites-core.php:235
msgid "Your favorites list is empty."
msgstr ""

#: includes/class-favorites-core.php:237
msgid "Continue Shopping"
msgstr ""

#: includes/class-favorites-ajax.php:35
msgid "Security check failed."
msgstr ""

#: includes/class-favorites-ajax.php:42
msgid "Invalid product ID."
msgstr ""

#: includes/class-favorites-ajax.php:49
msgid "Product not found."
msgstr ""

#: includes/class-favorites-ajax.php:58
msgid "Product removed from favorites."
msgstr ""

#: includes/class-favorites-ajax.php:64
msgid "Product added to favorites."
msgstr ""

#: includes/class-favorites-ajax.php:73
msgid "Failed to update favorites. Please try again."
msgstr ""

#: includes/class-favorites-ajax.php:105
msgid "Failed to remove product from favorites."
msgstr ""

#: includes/class-favorites-ajax.php:122
#, php-format
msgid "You have %d item in your favorites."
msgid_plural "You have %d items in your favorites."
msgstr[0] ""
msgstr[1] ""

#: includes/class-favorites-ajax.php:210
msgid "Your favorites list is empty"
msgstr ""

#: includes/class-favorites-ajax.php:211
msgid "Start adding products to your favorites by clicking the heart icon on any product."
msgstr ""

#: includes/class-favorites-ajax.php:215
msgid "Previous"
msgstr ""

#: includes/class-favorites-ajax.php:225
msgid "Next"
msgstr ""

#: includes/class-favorites-admin.php:45
msgid "WP Favorites Settings"
msgstr ""

#: includes/class-favorites-admin.php:46
msgid "WP Favorites"
msgstr ""

#: includes/class-favorites-admin.php:75
msgid "General Settings"
msgstr ""

#: includes/class-favorites-admin.php:82
msgid "Appearance Settings"
msgstr ""

#: includes/class-favorites-admin.php:89
msgid "Layout Settings"
msgstr ""

#: includes/class-favorites-admin.php:102
msgid "Favorites Page"
msgstr ""

#: includes/class-favorites-admin.php:109
msgid "Show on Shop Page"
msgstr ""

#: includes/class-favorites-admin.php:116
msgid "Show on Category Pages"
msgstr ""

#: includes/class-favorites-admin.php:123
msgid "Show on Single Product"
msgstr ""

#: includes/class-favorites-admin.php:131
msgid "Icon Position"
msgstr ""

#: includes/class-favorites-admin.php:138
msgid "Icon Size"
msgstr ""

#: includes/class-favorites-admin.php:145
msgid "Icon Colors"
msgstr ""

#: includes/class-favorites-admin.php:152
msgid "Custom Icon"
msgstr ""

#: includes/class-favorites-admin.php:160
msgid "Grid Columns"
msgstr ""

#: includes/class-favorites-admin.php:169
msgid "Configure general favorites functionality."
msgstr ""

#: includes/class-favorites-admin.php:173
msgid "Customize the appearance of favorites icons and buttons."
msgstr ""

#: includes/class-favorites-admin.php:177
msgid "Configure the layout of the favorites page."
msgstr ""

#: includes/class-favorites-admin.php:186
msgid "Select a page..."
msgstr ""

#: includes/class-favorites-admin.php:194
msgid "Select the page where favorites will be displayed. Add the shortcode [wp_favorites_list] to the page content."
msgstr ""

#: includes/class-favorites-admin.php:199
msgid "Show favorites icon on shop page products"
msgstr ""

#: includes/class-favorites-admin.php:206
msgid "Show favorites icon on category page products"
msgstr ""

#: includes/class-favorites-admin.php:213
msgid "Show favorites icon on single product pages"
msgstr ""

#: includes/class-favorites-admin.php:220
msgid "Top Left"
msgstr ""

#: includes/class-favorites-admin.php:221
msgid "Top Right"
msgstr ""

#: includes/class-favorites-admin.php:222
msgid "Bottom Left"
msgstr ""

#: includes/class-favorites-admin.php:223
msgid "Bottom Right"
msgstr ""

#: includes/class-favorites-admin.php:235
msgid "Small (30px)"
msgstr ""

#: includes/class-favorites-admin.php:236
msgid "Medium (40px)"
msgstr ""

#: includes/class-favorites-admin.php:237
msgid "Large (50px)"
msgstr ""

#: includes/class-favorites-admin.php:251
msgid "Default Color:"
msgstr ""

#: includes/class-favorites-admin.php:255
msgid "Active Color:"
msgstr ""

#: includes/class-favorites-admin.php:267
msgid "Custom Icon"
msgstr ""

#: includes/class-favorites-admin.php:269
msgid "No custom icon selected"
msgstr ""

#: includes/class-favorites-admin.php:274
msgid "Upload Icon"
msgstr ""

#: includes/class-favorites-admin.php:277
msgid "Remove Icon"
msgstr ""

#: includes/class-favorites-admin.php:280
msgid "Upload a custom icon for favorites. Recommended size: 24x24px. Supported formats: SVG, PNG, JPG."
msgstr ""

#: includes/class-favorites-admin.php:289
msgid "Desktop:"
msgstr ""

#: includes/class-favorites-admin.php:299
msgid "Tablet:"
msgstr ""

#: includes/class-favorites-admin.php:309
msgid "Mobile:"
msgstr ""

#: includes/class-favorites-admin.php:327
msgid "Settings saved successfully."
msgstr ""

#: includes/class-favorites-admin.php:338
msgid "General"
msgstr ""

#: includes/class-favorites-admin.php:342
msgid "Appearance"
msgstr ""

#: includes/class-favorites-admin.php:346
msgid "Layout"
msgstr ""

#: includes/class-favorites-admin.php:398
msgid "Security check failed."
msgstr ""

#: includes/class-favorites-admin.php:403
msgid "Insufficient permissions."
msgstr ""

#: includes/class-favorites-admin.php:409
msgid "Invalid attachment ID."
msgstr ""

#: includes/class-favorites-admin.php:414
msgid "Please select a valid image file."
msgstr ""

#: includes/class-favorites-admin.php:422
msgid "Icon uploaded successfully."
msgstr ""

#: includes/class-favorites-admin.php:429
msgid "Settings"
msgstr ""

#: includes/class-favorites-admin.php:440
#, php-format
msgid "Please select a page for displaying favorites in the %s."
msgstr ""

#: includes/class-favorites-admin.php:441
msgid "settings"
msgstr ""

#: includes/class-favorites-frontend.php:125
msgid "My Favorites"
msgstr ""

#: includes/class-favorites-breakdance.php:58
msgid "Favorites List"
msgstr ""

#: includes/class-favorites-breakdance.php:67
msgid "Favorites Button"
msgstr ""

#: includes/class-favorites-breakdance.php:76
msgid "Favorites Count"
msgstr ""

#: includes/class-favorites-breakdance.php:93
msgid "Content"
msgstr ""

#: includes/class-favorites-breakdance.php:96
msgid "Columns"
msgstr ""

#: includes/class-favorites-breakdance.php:108
msgid "Show Remove Button"
msgstr ""

#: includes/class-favorites-breakdance.php:113
msgid "Empty Message"
msgstr ""

#: includes/class-favorites-breakdance.php:118
msgid "Shop Button Text"
msgstr ""

#: includes/class-favorites-breakdance.php:124
msgid "Design"
msgstr ""

#: includes/class-favorites-breakdance.php:127
msgid "Gap"
msgstr ""

#: includes/class-favorites-breakdance.php:133
msgid "Card Style"
msgstr ""

#: includes/class-favorites-breakdance.php:136
msgid "Default"
msgstr ""

#: includes/class-favorites-breakdance.php:137
msgid "Minimal"
msgstr ""

#: includes/class-favorites-breakdance.php:138
msgid "Card"
msgstr ""

#: includes/class-favorites-breakdance.php:139
msgid "Modern"
msgstr ""

#: includes/class-favorites-breakdance.php:144
msgid "Border Radius"
msgstr ""

#: includes/class-favorites-breakdance.php:150
msgid "Shadow"
msgstr ""

#: includes/class-favorites-breakdance.php:162
msgid "Product ID"
msgstr ""

#: includes/class-favorites-breakdance.php:164
msgid "Leave empty for current product"
msgstr ""

#: includes/class-favorites-breakdance.php:167
msgid "Button Style"
msgstr ""

#: includes/class-favorites-breakdance.php:170
msgid "Icon Only"
msgstr ""

#: includes/class-favorites-breakdance.php:171
msgid "Text Only"
msgstr ""

#: includes/class-favorites-breakdance.php:172
msgid "Icon + Text"
msgstr ""

#: includes/class-favorites-breakdance.php:177
msgid "Add Text"
msgstr ""

#: includes/class-favorites-breakdance.php:179
msgid "Add to Favorites"
msgstr ""

#: includes/class-favorites-breakdance.php:183
msgid "Remove Text"
msgstr ""

#: includes/class-favorites-breakdance.php:185
msgid "Remove from Favorites"
msgstr ""

#: includes/class-favorites-breakdance.php:194
msgid "Size"
msgstr ""

#: includes/class-favorites-breakdance.php:200
msgid "Icon Size"
msgstr ""

#: includes/class-favorites-breakdance.php:206
msgid "Colors"
msgstr ""

#: includes/class-favorites-breakdance.php:209
msgid "Active"
msgstr ""

#: includes/class-favorites-breakdance.php:210
msgid "Background"
msgstr ""

#: includes/class-favorites-breakdance.php:211
msgid "Background Active"
msgstr ""

#: includes/class-favorites-breakdance.php:230
msgid "Display Style"
msgstr ""

#: includes/class-favorites-breakdance.php:233
msgid "Number Only"
msgstr ""

#: includes/class-favorites-breakdance.php:234
msgid "Badge"
msgstr ""

#: includes/class-favorites-breakdance.php:235
msgid "With Text"
msgstr ""

#: includes/class-favorites-breakdance.php:240
msgid "Text Template"
msgstr ""

#: includes/class-favorites-breakdance.php:242
msgid "{{count}} items"
msgstr ""

#: includes/class-favorites-breakdance.php:246
msgid "Hide When Empty"
msgstr ""

#: includes/class-favorites-breakdance.php:256
msgid "Typography"
msgstr ""

#: includes/class-favorites-breakdance.php:262
msgid "Text"
msgstr ""

#: includes/class-favorites-breakdance.php:268
msgid "Padding"
msgstr ""
