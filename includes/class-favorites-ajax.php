<?php
/**
 * WP Favorites AJAX Class
 * 
 * Handles AJAX requests for favorites functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Ajax {
    
    /**
     * Single instance
     */
    private static $instance = null;
    
    /**
     * Core instance
     */
    private $core;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->core = WP_Favorites_Core::get_instance();
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // AJAX actions for logged in users
        add_action('wp_ajax_wp_favorites_toggle', array($this, 'toggle_favorite'));
        add_action('wp_ajax_wp_favorites_remove', array($this, 'remove_favorite'));
        add_action('wp_ajax_wp_favorites_get_count', array($this, 'get_favorites_count'));
        add_action('wp_ajax_wp_favorites_get_list', array($this, 'get_favorites_list'));
        
        // AJAX actions for non-logged in users
        add_action('wp_ajax_nopriv_wp_favorites_toggle', array($this, 'toggle_favorite'));
        add_action('wp_ajax_nopriv_wp_favorites_remove', array($this, 'remove_favorite'));
        add_action('wp_ajax_nopriv_wp_favorites_get_count', array($this, 'get_favorites_count'));
        add_action('wp_ajax_nopriv_wp_favorites_get_list', array($this, 'get_favorites_list'));
    }
    
    /**
     * Toggle favorite status
     */
    public function toggle_favorite() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_favorites_nonce')) {
            wp_send_json_error(array(
                'message' => __('Security check failed.', 'wp-favorites')
            ));
        }
        
        // Get product ID
        $product_id = intval($_POST['product_id']);
        if (!$product_id) {
            wp_send_json_error(array(
                'message' => __('Invalid product ID.', 'wp-favorites')
            ));
        }
        
        // Check if product exists
        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error(array(
                'message' => __('Product not found.', 'wp-favorites')
            ));
        }
        
        // Toggle favorite status
        $is_favorite = $this->core->is_favorite($product_id);
        
        if ($is_favorite) {
            $result = $this->core->remove_favorite($product_id);
            $action = 'removed';
            $message = __('Product removed from favorites.', 'wp-favorites');
            $button_title = __('Add to favorites', 'wp-favorites');
        } else {
            $result = $this->core->add_favorite($product_id);
            $action = 'added';
            $message = __('Product added to favorites.', 'wp-favorites');
            $button_title = __('Remove from favorites', 'wp-favorites');
        }
        
        if ($result) {
            wp_send_json_success(array(
                'action' => $action,
                'message' => $message,
                'is_favorite' => !$is_favorite,
                'button_title' => $button_title,
                'icon_html' => $this->core->get_favorites_icon(!$is_favorite),
                'favorites_count' => count($this->core->get_user_favorites())
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to update favorites. Please try again.', 'wp-favorites')
            ));
        }
    }
    
    /**
     * Remove favorite
     */
    public function remove_favorite() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_favorites_nonce')) {
            wp_send_json_error(array(
                'message' => __('Security check failed.', 'wp-favorites')
            ));
        }
        
        // Get product ID
        $product_id = intval($_POST['product_id']);
        if (!$product_id) {
            wp_send_json_error(array(
                'message' => __('Invalid product ID.', 'wp-favorites')
            ));
        }
        
        // Remove from favorites
        $result = $this->core->remove_favorite($product_id);
        
        if ($result) {
            wp_send_json_success(array(
                'message' => __('Product removed from favorites.', 'wp-favorites'),
                'favorites_count' => count($this->core->get_user_favorites())
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to remove product from favorites.', 'wp-favorites')
            ));
        }
    }
    
    /**
     * Get favorites count
     */
    public function get_favorites_count() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_favorites_nonce')) {
            wp_send_json_error(array(
                'message' => __('Security check failed.', 'wp-favorites')
            ));
        }
        
        $favorites = $this->core->get_user_favorites();
        $count = count($favorites);
        
        wp_send_json_success(array(
            'count' => $count,
            'message' => sprintf(
                _n(
                    'You have %d item in your favorites.',
                    'You have %d items in your favorites.',
                    $count,
                    'wp-favorites'
                ),
                $count
            )
        ));
    }
    
    /**
     * Get favorites list (for AJAX loading)
     */
    public function get_favorites_list() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_favorites_nonce')) {
            wp_send_json_error(array(
                'message' => __('Security check failed.', 'wp-favorites')
            ));
        }
        
        // Get parameters
        $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $per_page = isset($_POST['per_page']) ? intval($_POST['per_page']) : 12;
        $columns = isset($_POST['columns']) ? intval($_POST['columns']) : 4;
        
        // Get favorites
        $favorites = $this->core->get_user_favorites();
        
        if (empty($favorites)) {
            wp_send_json_success(array(
                'html' => $this->get_empty_favorites_html(),
                'total' => 0,
                'pages' => 0,
                'current_page' => 1
            ));
        }
        
        // Calculate pagination
        $total = count($favorites);
        $pages = ceil($total / $per_page);
        $offset = ($page - 1) * $per_page;
        $favorites_page = array_slice($favorites, $offset, $per_page);
        
        // Generate HTML
        ob_start();
        echo '<div class="wp-favorites-grid" data-columns="' . esc_attr($columns) . '">';
        
        foreach ($favorites_page as $product_id) {
            $product = wc_get_product($product_id);
            if (!$product) continue;
            
            $this->render_favorite_item_ajax($product);
        }
        
        echo '</div>';
        
        // Add pagination if needed
        if ($pages > 1) {
            $this->render_pagination($page, $pages);
        }
        
        $html = ob_get_clean();
        
        wp_send_json_success(array(
            'html' => $html,
            'total' => $total,
            'pages' => $pages,
            'current_page' => $page
        ));
    }
    
    /**
     * Render favorite item for AJAX
     */
    private function render_favorite_item_ajax($product) {
        $core = WP_Favorites_Core::get_instance();
        $core->render_favorite_item($product, true);
    }
    
    /**
     * Get empty favorites HTML
     */
    private function get_empty_favorites_html() {
        ob_start();
        ?>
        <div class="wp-favorites-empty">
            <div class="wp-favorites-empty-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
            </div>
            <h3><?php _e('Your favorites list is empty', 'wp-favorites'); ?></h3>
            <p><?php _e('Start adding products to your favorites by clicking the heart icon on any product.', 'wp-favorites'); ?></p>
            <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" class="button wp-favorites-shop-button">
                <?php _e('Continue Shopping', 'wp-favorites'); ?>
            </a>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render pagination
     */
    private function render_pagination($current_page, $total_pages) {
        if ($total_pages <= 1) return;
        
        ?>
        <div class="wp-favorites-pagination">
            <?php if ($current_page > 1): ?>
                <button class="wp-favorites-page-btn" data-page="<?php echo ($current_page - 1); ?>">
                    <?php _e('Previous', 'wp-favorites'); ?>
                </button>
            <?php endif; ?>
            
            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <button class="wp-favorites-page-btn <?php echo ($i === $current_page) ? 'current' : ''; ?>" 
                        data-page="<?php echo $i; ?>">
                    <?php echo $i; ?>
                </button>
            <?php endfor; ?>
            
            <?php if ($current_page < $total_pages): ?>
                <button class="wp-favorites-page-btn" data-page="<?php echo ($current_page + 1); ?>">
                    <?php _e('Next', 'wp-favorites'); ?>
                </button>
            <?php endif; ?>
        </div>
        <?php
    }
}
