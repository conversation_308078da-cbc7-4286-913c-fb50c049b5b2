<?php
/**
 * WP Favorites Breakdance Integration Class
 * 
 * Handles Breakdance builder integration for the favorites system
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Breakdance {
    
    /**
     * Single instance
     */
    private static $instance = null;
    
    /**
     * Core instance
     */
    private $core;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->core = WP_Favorites_Core::get_instance();
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Register Breakdance elements
        add_action('breakdance_register_elements', array($this, 'register_elements'));
        
        // Add custom CSS for Breakdance
        add_action('breakdance_element_css', array($this, 'element_css'), 10, 2);
        
        // Enqueue Breakdance-specific scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_breakdance_scripts'));
    }
    
    /**
     * Register Breakdance elements
     */
    public function register_elements() {
        // Register Favorites List element
        \Breakdance\ElementsAPI\register_element([
            'name' => 'WPFavoritesList',
            'label' => __('Favorites List', 'wp-favorites'),
            'category' => 'WooCommerce',
            'icon' => $this->get_element_icon(),
            'controls' => $this->get_favorites_list_controls(),
            'render' => array($this, 'render_favorites_list'),
            'dependencies' => ['woocommerce']
        ]);
        
        // Register Favorites Button element
        \Breakdance\ElementsAPI\register_element([
            'name' => 'WPFavoritesButton',
            'label' => __('Favorites Button', 'wp-favorites'),
            'category' => 'WooCommerce',
            'icon' => $this->get_element_icon(),
            'controls' => $this->get_favorites_button_controls(),
            'render' => array($this, 'render_favorites_button'),
            'dependencies' => ['woocommerce']
        ]);
        
        // Register Favorites Count element
        \Breakdance\ElementsAPI\register_element([
            'name' => 'WPFavoritesCount',
            'label' => __('Favorites Count', 'wp-favorites'),
            'category' => 'WooCommerce',
            'icon' => $this->get_element_icon(),
            'controls' => $this->get_favorites_count_controls(),
            'render' => array($this, 'render_favorites_count'),
            'dependencies' => ['woocommerce']
        ]);
    }
    
    /**
     * Get element icon
     */
    private function get_element_icon() {
        return '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>';
    }
    
    /**
     * Get favorites list controls
     */
    private function get_favorites_list_controls() {
        return [
            'content' => [
                'label' => __('Content', 'wp-favorites'),
                'controls' => [
                    'columns' => [
                        'label' => __('Columns', 'wp-favorites'),
                        'type' => 'breakpoint_dropdown',
                        'items' => [
                            ['text' => '1', 'value' => '1'],
                            ['text' => '2', 'value' => '2'],
                            ['text' => '3', 'value' => '3'],
                            ['text' => '4', 'value' => '4'],
                            ['text' => '5', 'value' => '5'],
                            ['text' => '6', 'value' => '6']
                        ],
                        'defaultValue' => '4'
                    ],
                    'show_remove_button' => [
                        'label' => __('Show Remove Button', 'wp-favorites'),
                        'type' => 'toggle',
                        'defaultValue' => true
                    ],
                    'empty_message' => [
                        'label' => __('Empty Message', 'wp-favorites'),
                        'type' => 'text',
                        'defaultValue' => __('Your favorites list is empty.', 'wp-favorites')
                    ],
                    'shop_button_text' => [
                        'label' => __('Shop Button Text', 'wp-favorites'),
                        'type' => 'text',
                        'defaultValue' => __('Continue Shopping', 'wp-favorites')
                    ]
                ]
            ],
            'design' => [
                'label' => __('Design', 'wp-favorites'),
                'controls' => [
                    'gap' => [
                        'label' => __('Gap', 'wp-favorites'),
                        'type' => 'unit',
                        'unitOptions' => ['px', 'em', 'rem', '%'],
                        'defaultValue' => '20px'
                    ],
                    'card_style' => [
                        'label' => __('Card Style', 'wp-favorites'),
                        'type' => 'dropdown',
                        'items' => [
                            ['text' => __('Default', 'wp-favorites'), 'value' => 'default'],
                            ['text' => __('Minimal', 'wp-favorites'), 'value' => 'minimal'],
                            ['text' => __('Card', 'wp-favorites'), 'value' => 'card'],
                            ['text' => __('Modern', 'wp-favorites'), 'value' => 'modern']
                        ],
                        'defaultValue' => 'default'
                    ],
                    'border_radius' => [
                        'label' => __('Border Radius', 'wp-favorites'),
                        'type' => 'unit',
                        'unitOptions' => ['px', 'em', 'rem', '%'],
                        'defaultValue' => '8px'
                    ],
                    'shadow' => [
                        'label' => __('Shadow', 'wp-favorites'),
                        'type' => 'shadow'
                    ]
                ]
            ]
        ];
    }
    
    /**
     * Get favorites button controls
     */
    private function get_favorites_button_controls() {
        return [
            'content' => [
                'label' => __('Content', 'wp-favorites'),
                'controls' => [
                    'product_id' => [
                        'label' => __('Product ID', 'wp-favorites'),
                        'type' => 'number',
                        'placeholder' => __('Leave empty for current product', 'wp-favorites')
                    ],
                    'button_style' => [
                        'label' => __('Button Style', 'wp-favorites'),
                        'type' => 'dropdown',
                        'items' => [
                            ['text' => __('Icon Only', 'wp-favorites'), 'value' => 'icon'],
                            ['text' => __('Text Only', 'wp-favorites'), 'value' => 'text'],
                            ['text' => __('Icon + Text', 'wp-favorites'), 'value' => 'both']
                        ],
                        'defaultValue' => 'icon'
                    ],
                    'add_text' => [
                        'label' => __('Add Text', 'wp-favorites'),
                        'type' => 'text',
                        'defaultValue' => __('Add to Favorites', 'wp-favorites'),
                        'condition' => ['button_style' => ['text', 'both']]
                    ],
                    'remove_text' => [
                        'label' => __('Remove Text', 'wp-favorites'),
                        'type' => 'text',
                        'defaultValue' => __('Remove from Favorites', 'wp-favorites'),
                        'condition' => ['button_style' => ['text', 'both']]
                    ]
                ]
            ],
            'design' => [
                'label' => __('Design', 'wp-favorites'),
                'controls' => [
                    'size' => [
                        'label' => __('Size', 'wp-favorites'),
                        'type' => 'unit',
                        'unitOptions' => ['px', 'em', 'rem'],
                        'defaultValue' => '40px'
                    ],
                    'icon_size' => [
                        'label' => __('Icon Size', 'wp-favorites'),
                        'type' => 'unit',
                        'unitOptions' => ['px', 'em', 'rem'],
                        'defaultValue' => '20px'
                    ],
                    'colors' => [
                        'label' => __('Colors', 'wp-favorites'),
                        'type' => 'color_palette',
                        'items' => [
                            ['label' => __('Default', 'wp-favorites'), 'property' => 'default_color'],
                            ['label' => __('Active', 'wp-favorites'), 'property' => 'active_color'],
                            ['label' => __('Background', 'wp-favorites'), 'property' => 'background_color'],
                            ['label' => __('Background Active', 'wp-favorites'), 'property' => 'background_active_color']
                        ]
                    ],
                    'border_radius' => [
                        'label' => __('Border Radius', 'wp-favorites'),
                        'type' => 'unit',
                        'unitOptions' => ['px', 'em', 'rem', '%'],
                        'defaultValue' => '50%'
                    ]
                ]
            ]
        ];
    }
    
    /**
     * Get favorites count controls
     */
    private function get_favorites_count_controls() {
        return [
            'content' => [
                'label' => __('Content', 'wp-favorites'),
                'controls' => [
                    'display_style' => [
                        'label' => __('Display Style', 'wp-favorites'),
                        'type' => 'dropdown',
                        'items' => [
                            ['text' => __('Number Only', 'wp-favorites'), 'value' => 'number'],
                            ['text' => __('Badge', 'wp-favorites'), 'value' => 'badge'],
                            ['text' => __('With Text', 'wp-favorites'), 'value' => 'text']
                        ],
                        'defaultValue' => 'badge'
                    ],
                    'text_template' => [
                        'label' => __('Text Template', 'wp-favorites'),
                        'type' => 'text',
                        'defaultValue' => __('{{count}} items', 'wp-favorites'),
                        'condition' => ['display_style' => 'text']
                    ],
                    'hide_when_empty' => [
                        'label' => __('Hide When Empty', 'wp-favorites'),
                        'type' => 'toggle',
                        'defaultValue' => true
                    ]
                ]
            ],
            'design' => [
                'label' => __('Design', 'wp-favorites'),
                'controls' => [
                    'typography' => [
                        'label' => __('Typography', 'wp-favorites'),
                        'type' => 'typography'
                    ],
                    'colors' => [
                        'label' => __('Colors', 'wp-favorites'),
                        'type' => 'color_palette',
                        'items' => [
                            ['label' => __('Text', 'wp-favorites'), 'property' => 'text_color'],
                            ['label' => __('Background', 'wp-favorites'), 'property' => 'background_color']
                        ]
                    ],
                    'padding' => [
                        'label' => __('Padding', 'wp-favorites'),
                        'type' => 'spacing'
                    ],
                    'border_radius' => [
                        'label' => __('Border Radius', 'wp-favorites'),
                        'type' => 'unit',
                        'unitOptions' => ['px', 'em', 'rem', '%']
                    ]
                ]
            ]
        ];
    }
    
    /**
     * Render favorites list element
     */
    public function render_favorites_list($element) {
        $settings = $element['settings'] ?? [];
        
        $columns = $settings['content']['columns'] ?? '4';
        $show_remove = $settings['content']['show_remove_button'] ?? true;
        $empty_message = $settings['content']['empty_message'] ?? __('Your favorites list is empty.', 'wp-favorites');
        $shop_button_text = $settings['content']['shop_button_text'] ?? __('Continue Shopping', 'wp-favorites');
        
        $favorites = $this->core->get_user_favorites();
        
        echo '<div class="wp-favorites-breakdance-list" data-columns="' . esc_attr($columns) . '">';
        
        if (empty($favorites)) {
            echo '<div class="wp-favorites-empty">';
            echo '<p>' . esc_html($empty_message) . '</p>';
            echo '<a href="' . esc_url(wc_get_page_permalink('shop')) . '" class="button wp-favorites-shop-button">';
            echo esc_html($shop_button_text);
            echo '</a>';
            echo '</div>';
        } else {
            echo '<div class="wp-favorites-grid" data-columns="' . esc_attr($columns) . '">';
            
            foreach ($favorites as $product_id) {
                $product = wc_get_product($product_id);
                if (!$product) continue;
                
                $this->render_breakdance_favorite_item($product, $show_remove);
            }
            
            echo '</div>';
        }
        
        echo '</div>';
    }
    
    /**
     * Render favorites button element
     */
    public function render_favorites_button($element) {
        $settings = $element['settings'] ?? [];
        
        $product_id = $settings['content']['product_id'] ?? 0;
        $button_style = $settings['content']['button_style'] ?? 'icon';
        $add_text = $settings['content']['add_text'] ?? __('Add to Favorites', 'wp-favorites');
        $remove_text = $settings['content']['remove_text'] ?? __('Remove from Favorites', 'wp-favorites');
        
        // Get product
        if (!$product_id) {
            global $product;
            if (!$product || !is_a($product, 'WC_Product')) {
                return;
            }
            $product_id = $product->get_id();
        } else {
            $product = wc_get_product($product_id);
            if (!$product) {
                return;
            }
        }
        
        $is_favorite = $this->core->is_favorite($product_id);
        $icon_class = $is_favorite ? 'wp-favorites-icon active' : 'wp-favorites-icon';
        $title = $is_favorite ? $remove_text : $add_text;
        
        echo '<div class="wp-favorites-breakdance-button" data-product-id="' . esc_attr($product_id) . '">';
        echo '<button class="' . esc_attr($icon_class) . ' style-' . esc_attr($button_style) . '" ';
        echo 'title="' . esc_attr($title) . '" ';
        echo 'aria-label="' . esc_attr($title) . '" ';
        echo 'data-product-id="' . esc_attr($product_id) . '">';
        
        if ($button_style === 'icon' || $button_style === 'both') {
            echo $this->core->get_favorites_icon($is_favorite);
        }
        
        if ($button_style === 'text' || $button_style === 'both') {
            echo '<span class="button-text">' . esc_html($title) . '</span>';
        }
        
        echo '</button>';
        echo '</div>';
    }
    
    /**
     * Render favorites count element
     */
    public function render_favorites_count($element) {
        $settings = $element['settings'] ?? [];
        
        $display_style = $settings['content']['display_style'] ?? 'badge';
        $text_template = $settings['content']['text_template'] ?? __('{{count}} items', 'wp-favorites');
        $hide_when_empty = $settings['content']['hide_when_empty'] ?? true;
        
        $count = count($this->core->get_user_favorites());
        
        if ($hide_when_empty && $count === 0) {
            return;
        }
        
        echo '<div class="wp-favorites-breakdance-count style-' . esc_attr($display_style) . '">';
        
        switch ($display_style) {
            case 'number':
                echo '<span class="count-number">' . esc_html($count) . '</span>';
                break;
                
            case 'badge':
                echo '<span class="count-badge">' . esc_html($count) . '</span>';
                break;
                
            case 'text':
                $text = str_replace('{{count}}', $count, $text_template);
                echo '<span class="count-text">' . esc_html($text) . '</span>';
                break;
        }
        
        echo '</div>';
    }
    
    /**
     * Render favorite item for Breakdance
     */
    private function render_breakdance_favorite_item($product, $show_remove = true) {
        ?>
        <div class="wp-favorites-item breakdance-item" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
            <div class="wp-favorites-item-image">
                <a href="<?php echo esc_url($product->get_permalink()); ?>">
                    <?php echo $product->get_image('woocommerce_thumbnail'); ?>
                </a>
                <?php if ($show_remove): ?>
                <button class="wp-favorites-remove" 
                        title="<?php esc_attr_e('Remove from favorites', 'wp-favorites'); ?>"
                        data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                    <span>&times;</span>
                </button>
                <?php endif; ?>
            </div>
            <div class="wp-favorites-item-content">
                <h3 class="wp-favorites-item-title">
                    <a href="<?php echo esc_url($product->get_permalink()); ?>">
                        <?php echo esc_html($product->get_name()); ?>
                    </a>
                </h3>
                <div class="wp-favorites-item-price">
                    <?php echo $product->get_price_html(); ?>
                </div>
                <div class="wp-favorites-item-actions">
                    <?php woocommerce_template_loop_add_to_cart(); ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Add element CSS
     */
    public function element_css($element, $desktop_css) {
        if (!in_array($element['name'], ['WPFavoritesList', 'WPFavoritesButton', 'WPFavoritesCount'])) {
            return;
        }
        
        $settings = $element['settings'] ?? [];
        $selector = $element['selector'] ?? '';
        
        // Add element-specific CSS based on settings
        if ($element['name'] === 'WPFavoritesList') {
            $this->add_favorites_list_css($settings, $selector, $desktop_css);
        } elseif ($element['name'] === 'WPFavoritesButton') {
            $this->add_favorites_button_css($settings, $selector, $desktop_css);
        } elseif ($element['name'] === 'WPFavoritesCount') {
            $this->add_favorites_count_css($settings, $selector, $desktop_css);
        }
    }
    
    /**
     * Add favorites list CSS
     */
    private function add_favorites_list_css($settings, $selector, &$css) {
        $gap = $settings['design']['gap'] ?? '20px';
        $border_radius = $settings['design']['border_radius'] ?? '8px';
        
        $css[] = "$selector .wp-favorites-grid { gap: $gap; }";
        $css[] = "$selector .wp-favorites-item { border-radius: $border_radius; }";
        
        if (isset($settings['design']['shadow'])) {
            $shadow = $settings['design']['shadow'];
            $css[] = "$selector .wp-favorites-item { box-shadow: $shadow; }";
        }
    }
    
    /**
     * Add favorites button CSS
     */
    private function add_favorites_button_css($settings, $selector, &$css) {
        $size = $settings['design']['size'] ?? '40px';
        $icon_size = $settings['design']['icon_size'] ?? '20px';
        $border_radius = $settings['design']['border_radius'] ?? '50%';
        
        $css[] = "$selector .wp-favorites-icon { width: $size; height: $size; border-radius: $border_radius; }";
        $css[] = "$selector .wp-favorites-heart, $selector .wp-favorites-custom-icon { width: $icon_size; height: $icon_size; }";
        
        if (isset($settings['design']['colors'])) {
            $colors = $settings['design']['colors'];
            if (isset($colors['default_color'])) {
                $css[] = "$selector .wp-favorites-heart.empty { stroke: {$colors['default_color']}; }";
            }
            if (isset($colors['active_color'])) {
                $css[] = "$selector .wp-favorites-heart.filled { fill: {$colors['active_color']}; stroke: {$colors['active_color']}; }";
            }
            if (isset($colors['background_color'])) {
                $css[] = "$selector .wp-favorites-icon { background: {$colors['background_color']}; }";
            }
            if (isset($colors['background_active_color'])) {
                $css[] = "$selector .wp-favorites-icon.active { background: {$colors['background_active_color']}; }";
            }
        }
    }
    
    /**
     * Add favorites count CSS
     */
    private function add_favorites_count_css($settings, $selector, &$css) {
        if (isset($settings['design']['colors'])) {
            $colors = $settings['design']['colors'];
            if (isset($colors['text_color'])) {
                $css[] = "$selector { color: {$colors['text_color']}; }";
            }
            if (isset($colors['background_color'])) {
                $css[] = "$selector { background: {$colors['background_color']}; }";
            }
        }
        
        if (isset($settings['design']['border_radius'])) {
            $border_radius = $settings['design']['border_radius'];
            $css[] = "$selector { border-radius: $border_radius; }";
        }
    }
    
    /**
     * Enqueue Breakdance-specific scripts
     */
    public function enqueue_breakdance_scripts() {
        if (!function_exists('breakdance_is_builder_preview') || !breakdance_is_builder_preview()) {
            return;
        }
        
        // Enqueue additional scripts for Breakdance builder
        wp_enqueue_script(
            'wp-favorites-breakdance',
            WP_FAVORITES_PLUGIN_URL . 'assets/js/breakdance-integration.js',
            array('jquery', 'wp-favorites-frontend'),
            WP_FAVORITES_VERSION,
            true
        );
    }
}
