<?php
/**
 * WP Favorites Translations Class
 * 
 * Handles manual translation management for the favorites system
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Translations {
    
    /**
     * Single instance
     */
    private static $instance = null;
    
    /**
     * Translations table name
     */
    private $table_name;
    
    /**
     * Available languages
     */
    private $available_languages = array(
        'en_US' => 'English (US)',
        'pt_PT' => 'Português (Portugal)',
        'pt_BR' => 'Português (Brasil)',
        'es_ES' => 'Español',
        'fr_FR' => 'Français',
        'de_DE' => 'Deutsch',
        'it_IT' => 'Italiano'
    );
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'wp_favorites_translations';
        
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Override translation functions
        add_filter('gettext', array($this, 'override_translation'), 10, 3);
        add_filter('gettext_with_context', array($this, 'override_translation_with_context'), 10, 4);
        add_filter('ngettext', array($this, 'override_plural_translation'), 10, 5);
        
        // AJAX handlers for translation management
        add_action('wp_ajax_wp_favorites_save_translation', array($this, 'save_translation'));
        add_action('wp_ajax_wp_favorites_delete_translation', array($this, 'delete_translation'));
        add_action('wp_ajax_wp_favorites_export_translations', array($this, 'export_translations'));
        add_action('wp_ajax_wp_favorites_import_translations', array($this, 'import_translations'));
    }
    
    /**
     * Override gettext translations
     */
    public function override_translation($translation, $text, $domain) {
        if ($domain !== 'wp-favorites') {
            return $translation;
        }
        
        $custom_translation = $this->get_custom_translation($text, get_locale());
        return $custom_translation ? $custom_translation : $translation;
    }
    
    /**
     * Override gettext_with_context translations
     */
    public function override_translation_with_context($translation, $text, $context, $domain) {
        if ($domain !== 'wp-favorites') {
            return $translation;
        }
        
        $custom_translation = $this->get_custom_translation($text, get_locale(), $context);
        return $custom_translation ? $custom_translation : $translation;
    }
    
    /**
     * Override ngettext plural translations
     */
    public function override_plural_translation($translation, $single, $plural, $number, $domain) {
        if ($domain !== 'wp-favorites') {
            return $translation;
        }
        
        $text = ($number == 1) ? $single : $plural;
        $custom_translation = $this->get_custom_translation($text, get_locale());
        return $custom_translation ? $custom_translation : $translation;
    }
    
    /**
     * Get custom translation from database
     */
    public function get_custom_translation($original_text, $language_code, $context = '') {
        global $wpdb;
        
        $string_key = $this->generate_string_key($original_text, $context);
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT translated_text FROM {$this->table_name} 
             WHERE string_key = %s AND language_code = %s AND context = %s",
            $string_key,
            $language_code,
            $context
        ));
        
        return $result;
    }
    
    /**
     * Save custom translation
     */
    public function save_custom_translation($original_text, $translated_text, $language_code, $context = '') {
        global $wpdb;
        
        $string_key = $this->generate_string_key($original_text, $context);
        
        $data = array(
            'string_key' => $string_key,
            'original_text' => $original_text,
            'translated_text' => $translated_text,
            'language_code' => $language_code,
            'context' => $context
        );
        
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$this->table_name} 
             WHERE string_key = %s AND language_code = %s AND context = %s",
            $string_key,
            $language_code,
            $context
        ));
        
        if ($existing) {
            return $wpdb->update(
                $this->table_name,
                array('translated_text' => $translated_text, 'date_modified' => current_time('mysql')),
                array('id' => $existing),
                array('%s', '%s'),
                array('%d')
            );
        } else {
            return $wpdb->insert($this->table_name, $data);
        }
    }
    
    /**
     * Delete custom translation
     */
    public function delete_custom_translation($string_key, $language_code, $context = '') {
        global $wpdb;
        
        return $wpdb->delete(
            $this->table_name,
            array(
                'string_key' => $string_key,
                'language_code' => $language_code,
                'context' => $context
            ),
            array('%s', '%s', '%s')
        );
    }
    
    /**
     * Get all translations for a language
     */
    public function get_translations_by_language($language_code) {
        global $wpdb;
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE language_code = %s ORDER BY original_text",
            $language_code
        ));
    }
    
    /**
     * Get all translatable strings from POT file
     */
    public function get_all_translatable_strings() {
        $pot_file = WP_FAVORITES_PLUGIN_DIR . 'languages/wp-favorites.pot';
        $strings = array();
        
        if (!file_exists($pot_file)) {
            return $strings;
        }
        
        $content = file_get_contents($pot_file);
        
        // Parse POT file for msgid entries
        preg_match_all('/msgid\s+"([^"]+)"/', $content, $matches);
        
        if (!empty($matches[1])) {
            foreach ($matches[1] as $string) {
                if (!empty($string) && $string !== '') {
                    $strings[] = $string;
                }
            }
        }
        
        return array_unique($strings);
    }
    
    /**
     * Generate unique string key
     */
    public function generate_string_key($text, $context = '') {
        return md5($text . '|' . $context);
    }
    
    /**
     * Get available languages
     */
    public function get_available_languages() {
        return $this->available_languages;
    }
    
    /**
     * AJAX: Save translation
     */
    public function save_translation() {
        if (!wp_verify_nonce($_POST['nonce'], 'wp_favorites_admin_nonce')) {
            wp_send_json_error(__('Security check failed.', 'wp-favorites'));
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'wp-favorites'));
        }
        
        $original_text = sanitize_text_field($_POST['original_text']);
        $translated_text = sanitize_textarea_field($_POST['translated_text']);
        $language_code = sanitize_text_field($_POST['language_code']);
        $context = sanitize_text_field($_POST['context']);
        
        if (empty($original_text) || empty($language_code)) {
            wp_send_json_error(__('Original text and language are required.', 'wp-favorites'));
        }
        
        $result = $this->save_custom_translation($original_text, $translated_text, $language_code, $context);
        
        if ($result !== false) {
            wp_send_json_success(__('Translation saved successfully.', 'wp-favorites'));
        } else {
            wp_send_json_error(__('Failed to save translation.', 'wp-favorites'));
        }
    }
    
    /**
     * AJAX: Delete translation
     */
    public function delete_translation() {
        if (!wp_verify_nonce($_POST['nonce'], 'wp_favorites_admin_nonce')) {
            wp_send_json_error(__('Security check failed.', 'wp-favorites'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'wp-favorites'));
        }

        $string_key = sanitize_text_field($_POST['string_key']);
        $language_code = sanitize_text_field($_POST['language_code']);
        $context = sanitize_text_field($_POST['context']);

        $result = $this->delete_custom_translation($string_key, $language_code, $context);

        if ($result !== false) {
            wp_send_json_success(__('Translation deleted successfully.', 'wp-favorites'));
        } else {
            wp_send_json_error(__('Failed to delete translation.', 'wp-favorites'));
        }
    }

    /**
     * AJAX: Export translations
     */
    public function export_translations() {
        if (!wp_verify_nonce($_POST['nonce'], 'wp_favorites_admin_nonce')) {
            wp_send_json_error(__('Security check failed.', 'wp-favorites'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'wp-favorites'));
        }

        $language_code = sanitize_text_field($_POST['language_code']);
        $translations = $this->get_translations_by_language($language_code);

        $export_data = array(
            'language' => $language_code,
            'plugin' => 'wp-favorites',
            'version' => WP_FAVORITES_VERSION,
            'exported_at' => current_time('mysql'),
            'translations' => array()
        );

        foreach ($translations as $translation) {
            $export_data['translations'][] = array(
                'original' => $translation->original_text,
                'translated' => $translation->translated_text,
                'context' => $translation->context
            );
        }

        wp_send_json_success(array(
            'data' => $export_data,
            'filename' => "wp-favorites-{$language_code}-" . date('Y-m-d') . '.json'
        ));
    }

    /**
     * AJAX: Import translations
     */
    public function import_translations() {
        if (!wp_verify_nonce($_POST['nonce'], 'wp_favorites_admin_nonce')) {
            wp_send_json_error(__('Security check failed.', 'wp-favorites'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'wp-favorites'));
        }

        if (empty($_POST['import_data'])) {
            wp_send_json_error(__('No import data provided.', 'wp-favorites'));
        }

        $import_data = json_decode(stripslashes($_POST['import_data']), true);

        if (!$import_data || !isset($import_data['translations'])) {
            wp_send_json_error(__('Invalid import data format.', 'wp-favorites'));
        }

        $language_code = sanitize_text_field($import_data['language']);
        $imported_count = 0;
        $errors = array();

        foreach ($import_data['translations'] as $translation) {
            $original = sanitize_text_field($translation['original']);
            $translated = sanitize_textarea_field($translation['translated']);
            $context = sanitize_text_field($translation['context']);

            if (!empty($original) && !empty($translated)) {
                $result = $this->save_custom_translation($original, $translated, $language_code, $context);
                if ($result !== false) {
                    $imported_count++;
                } else {
                    $errors[] = sprintf(__('Failed to import: %s', 'wp-favorites'), $original);
                }
            }
        }

        if ($imported_count > 0) {
            $message = sprintf(__('%d translations imported successfully.', 'wp-favorites'), $imported_count);
            if (!empty($errors)) {
                $message .= ' ' . sprintf(__('%d errors occurred.', 'wp-favorites'), count($errors));
            }
            wp_send_json_success(array('message' => $message, 'errors' => $errors));
        } else {
            wp_send_json_error(__('No translations were imported.', 'wp-favorites'));
        }
    }
}
