<?php
/**
 * WP Favorites Frontend Class
 * 
 * Handles frontend functionality for the favorites system
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Frontend {
    
    /**
     * Single instance
     */
    private static $instance = null;
    
    /**
     * Core instance
     */
    private $core;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->core = WP_Favorites_Core::get_instance();
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add favorites button to WooCommerce product loops
        add_action('woocommerce_before_shop_loop_item_title', array($this, 'add_favorites_button_to_loop'), 5);
        add_action('woocommerce_before_single_product_summary', array($this, 'add_favorites_button_to_single'), 25);
        
        // Add custom CSS for positioning
        add_action('wp_head', array($this, 'add_custom_css'));
        
        // Handle favorites page content
        add_filter('the_content', array($this, 'handle_favorites_page_content'));
        
        // Add body classes
        add_filter('body_class', array($this, 'add_body_classes'));
        
        // Add to cart fragments for AJAX cart
        add_filter('woocommerce_add_to_cart_fragments', array($this, 'add_cart_fragments'));
    }
    
    /**
     * Add favorites button to shop loop
     */
    public function add_favorites_button_to_loop() {
        if (get_option('wp_favorites_show_on_shop') === 'yes' && (is_shop() || is_product_category() || is_product_tag())) {
            $this->render_favorites_button();
        }
    }
    
    /**
     * Add favorites button to single product
     */
    public function add_favorites_button_to_single() {
        if (get_option('wp_favorites_show_on_single') === 'yes' && is_product()) {
            $this->render_favorites_button();
        }
    }
    
    /**
     * Render favorites button
     */
    public function render_favorites_button() {
        global $product;
        
        if (!$product || !is_a($product, 'WC_Product')) {
            return;
        }
        
        $product_id = $product->get_id();
        $is_favorite = $this->core->is_favorite($product_id);
        $icon_class = $is_favorite ? 'wp-favorites-icon active' : 'wp-favorites-icon';
        $title = $is_favorite ? __('Remove from favorites', 'wp-favorites') : __('Add to favorites', 'wp-favorites');
        
        // Get position and size settings
        $position = get_option('wp_favorites_icon_position', 'top-right');
        $size = get_option('wp_favorites_icon_size', 'medium');
        
        ?>
        <div class="wp-favorites-wrapper wp-favorites-position-<?php echo esc_attr($position); ?> wp-favorites-size-<?php echo esc_attr($size); ?>" 
             data-product-id="<?php echo esc_attr($product_id); ?>">
            <button class="<?php echo esc_attr($icon_class); ?>" 
                    title="<?php echo esc_attr($title); ?>"
                    aria-label="<?php echo esc_attr($title); ?>"
                    data-product-id="<?php echo esc_attr($product_id); ?>">
                <?php echo $this->get_favorites_icon($is_favorite); ?>
            </button>
        </div>
        <?php
    }
    
    /**
     * Get favorites icon HTML
     */
    public function get_favorites_icon($is_active = false) {
        $custom_icon_id = get_option('wp_favorites_custom_icon_id', 0);
        
        if ($custom_icon_id && $custom_icon_url = wp_get_attachment_url($custom_icon_id)) {
            return sprintf(
                '<img src="%s" alt="%s" class="wp-favorites-custom-icon %s">',
                esc_url($custom_icon_url),
                esc_attr__('Favorite', 'wp-favorites'),
                $is_active ? 'active' : ''
            );
        }
        
        // Default heart icon (SVG)
        $heart_class = $is_active ? 'filled' : 'empty';
        $color = $is_active ? get_option('wp_favorites_icon_color_active', '#e74c3c') : get_option('wp_favorites_icon_color', '#666666');
        
        return sprintf(
            '<svg class="wp-favorites-heart %s" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="color: %s;">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </svg>',
            esc_attr($heart_class),
            esc_attr($color)
        );
    }
    
    /**
     * Add custom CSS for positioning and styling
     */
    public function add_custom_css() {
        $position = get_option('wp_favorites_icon_position', 'top-right');
        $size = get_option('wp_favorites_icon_size', 'medium');
        $color = get_option('wp_favorites_icon_color', '#666666');
        $color_active = get_option('wp_favorites_icon_color_active', '#e74c3c');
        
        // Size mappings
        $sizes = array(
            'small' => '30px',
            'medium' => '40px',
            'large' => '50px'
        );
        
        $icon_size = isset($sizes[$size]) ? $sizes[$size] : $sizes['medium'];
        $icon_inner_size = intval($icon_size) * 0.5 . 'px';
        
        ?>
        <style type="text/css">
        .wp-favorites-wrapper {
            position: absolute;
            z-index: 10;
        }
        
        .wp-favorites-position-top-left {
            top: 10px;
            left: 10px;
        }
        
        .wp-favorites-position-top-right {
            top: 10px;
            right: 10px;
        }
        
        .wp-favorites-position-bottom-left {
            bottom: 10px;
            left: 10px;
        }
        
        .wp-favorites-position-bottom-right {
            bottom: 10px;
            right: 10px;
        }
        
        .wp-favorites-size-<?php echo esc_attr($size); ?> .wp-favorites-icon {
            width: <?php echo esc_attr($icon_size); ?>;
            height: <?php echo esc_attr($icon_size); ?>;
        }
        
        .wp-favorites-size-<?php echo esc_attr($size); ?> .wp-favorites-heart,
        .wp-favorites-size-<?php echo esc_attr($size); ?> .wp-favorites-custom-icon {
            width: <?php echo esc_attr($icon_inner_size); ?>;
            height: <?php echo esc_attr($icon_inner_size); ?>;
        }
        
        .wp-favorites-heart.empty {
            stroke: <?php echo esc_attr($color); ?>;
        }
        
        .wp-favorites-heart.filled {
            fill: <?php echo esc_attr($color_active); ?>;
            stroke: <?php echo esc_attr($color_active); ?>;
        }
        
        .wp-favorites-icon:hover .wp-favorites-heart.empty {
            stroke: <?php echo esc_attr($color_active); ?>;
        }
        </style>
        <?php
    }
    
    /**
     * Handle favorites page content
     */
    public function handle_favorites_page_content($content) {
        $favorites_page_id = get_option('wp_favorites_favorites_page_id', 0);
        
        if ($favorites_page_id && is_page($favorites_page_id)) {
            // Check if shortcode is already in content
            if (strpos($content, '[wp_favorites_list]') === false) {
                // Add shortcode to content
                $content .= '[wp_favorites_list]';
            }
        }
        
        return $content;
    }
    
    /**
     * Add body classes
     */
    public function add_body_classes($classes) {
        $favorites_page_id = get_option('wp_favorites_favorites_page_id', 0);
        
        if ($favorites_page_id && is_page($favorites_page_id)) {
            $classes[] = 'wp-favorites-page';
        }
        
        if (is_shop() || is_product_category() || is_product_tag() || is_product()) {
            $classes[] = 'has-wp-favorites';
        }
        
        return $classes;
    }
    
    /**
     * Add cart fragments for AJAX cart
     */
    public function add_cart_fragments($fragments) {
        // Add favorites count to cart fragments
        $count = count($this->core->get_user_favorites());
        
        ob_start();
        ?>
        <span class="wp-favorites-count"><?php echo esc_html($count); ?></span>
        <?php
        $fragments['.wp-favorites-count'] = ob_get_clean();
        
        return $fragments;
    }
    
    /**
     * Get favorites page URL
     */
    public function get_favorites_page_url() {
        $page_id = get_option('wp_favorites_favorites_page_id', 0);
        
        if ($page_id) {
            return get_permalink($page_id);
        }
        
        return home_url();
    }
    
    /**
     * Display favorites count
     */
    public function display_favorites_count($echo = true) {
        $count = count($this->core->get_user_favorites());
        $output = '<span class="wp-favorites-count">' . esc_html($count) . '</span>';
        
        if ($echo) {
            echo $output;
        } else {
            return $output;
        }
    }
    
    /**
     * Display favorites link
     */
    public function display_favorites_link($text = '', $echo = true) {
        if (empty($text)) {
            $text = __('My Favorites', 'wp-favorites');
        }
        
        $url = $this->get_favorites_page_url();
        $count = count($this->core->get_user_favorites());
        
        $output = sprintf(
            '<a href="%s" class="wp-favorites-link">%s %s</a>',
            esc_url($url),
            esc_html($text),
            $this->display_favorites_count(false)
        );
        
        if ($echo) {
            echo $output;
        } else {
            return $output;
        }
    }
    
    /**
     * Check if current page is favorites page
     */
    public function is_favorites_page() {
        $favorites_page_id = get_option('wp_favorites_favorites_page_id', 0);
        return $favorites_page_id && is_page($favorites_page_id);
    }
    
    /**
     * Get favorites page title
     */
    public function get_favorites_page_title() {
        $favorites_page_id = get_option('wp_favorites_favorites_page_id', 0);
        
        if ($favorites_page_id) {
            return get_the_title($favorites_page_id);
        }
        
        return __('My Favorites', 'wp-favorites');
    }
}
