<?php
/**
 * WP Favorites Admin Class
 * 
 * Handles admin functionality for the favorites system
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Admin {
    
    /**
     * Single instance
     */
    private static $instance = null;
    
    /**
     * Settings page slug
     */
    private $page_slug = 'wp-favorites-settings';
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Register settings
        add_action('admin_init', array($this, 'register_settings'));
        
        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Handle file uploads
        add_action('wp_ajax_wp_favorites_upload_icon', array($this, 'handle_icon_upload'));
        
        // Add settings link to plugins page
        add_filter('plugin_action_links_' . WP_FAVORITES_PLUGIN_BASENAME, array($this, 'add_settings_link'));
        
        // Add admin notices
        add_action('admin_notices', array($this, 'admin_notices'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('WP Favorites Settings', 'wp-favorites'),
            __('WP Favorites', 'wp-favorites'),
            'manage_options',
            $this->page_slug,
            array($this, 'render_admin_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        // Register setting groups
        register_setting('wp_favorites_general', 'wp_favorites_favorites_page_id');
        register_setting('wp_favorites_general', 'wp_favorites_show_on_shop');
        register_setting('wp_favorites_general', 'wp_favorites_show_on_category');
        register_setting('wp_favorites_general', 'wp_favorites_show_on_single');
        
        register_setting('wp_favorites_appearance', 'wp_favorites_icon_position');
        register_setting('wp_favorites_appearance', 'wp_favorites_icon_size');
        register_setting('wp_favorites_appearance', 'wp_favorites_icon_color');
        register_setting('wp_favorites_appearance', 'wp_favorites_icon_color_active');
        register_setting('wp_favorites_appearance', 'wp_favorites_custom_icon_id');
        
        register_setting('wp_favorites_layout', 'wp_favorites_grid_columns_desktop');
        register_setting('wp_favorites_layout', 'wp_favorites_grid_columns_tablet');
        register_setting('wp_favorites_layout', 'wp_favorites_grid_columns_mobile');
        
        // Add settings sections
        add_settings_section(
            'wp_favorites_general_section',
            __('General Settings', 'wp-favorites'),
            array($this, 'general_section_callback'),
            'wp_favorites_general'
        );
        
        add_settings_section(
            'wp_favorites_appearance_section',
            __('Appearance Settings', 'wp-favorites'),
            array($this, 'appearance_section_callback'),
            'wp_favorites_appearance'
        );
        
        add_settings_section(
            'wp_favorites_layout_section',
            __('Layout Settings', 'wp-favorites'),
            array($this, 'layout_section_callback'),
            'wp_favorites_layout'
        );
        
        // Add settings fields
        $this->add_settings_fields();
    }
    
    /**
     * Add settings fields
     */
    private function add_settings_fields() {
        // General settings
        add_settings_field(
            'favorites_page_id',
            __('Favorites Page', 'wp-favorites'),
            array($this, 'favorites_page_field'),
            'wp_favorites_general',
            'wp_favorites_general_section'
        );
        
        add_settings_field(
            'show_on_shop',
            __('Show on Shop Page', 'wp-favorites'),
            array($this, 'show_on_shop_field'),
            'wp_favorites_general',
            'wp_favorites_general_section'
        );
        
        add_settings_field(
            'show_on_category',
            __('Show on Category Pages', 'wp-favorites'),
            array($this, 'show_on_category_field'),
            'wp_favorites_general',
            'wp_favorites_general_section'
        );
        
        add_settings_field(
            'show_on_single',
            __('Show on Single Product', 'wp-favorites'),
            array($this, 'show_on_single_field'),
            'wp_favorites_general',
            'wp_favorites_general_section'
        );
        
        // Appearance settings
        add_settings_field(
            'icon_position',
            __('Icon Position', 'wp-favorites'),
            array($this, 'icon_position_field'),
            'wp_favorites_appearance',
            'wp_favorites_appearance_section'
        );
        
        add_settings_field(
            'icon_size',
            __('Icon Size', 'wp-favorites'),
            array($this, 'icon_size_field'),
            'wp_favorites_appearance',
            'wp_favorites_appearance_section'
        );
        
        add_settings_field(
            'icon_colors',
            __('Icon Colors', 'wp-favorites'),
            array($this, 'icon_colors_field'),
            'wp_favorites_appearance',
            'wp_favorites_appearance_section'
        );
        
        add_settings_field(
            'custom_icon',
            __('Custom Icon', 'wp-favorites'),
            array($this, 'custom_icon_field'),
            'wp_favorites_appearance',
            'wp_favorites_appearance_section'
        );
        
        // Layout settings
        add_settings_field(
            'grid_columns',
            __('Grid Columns', 'wp-favorites'),
            array($this, 'grid_columns_field'),
            'wp_favorites_layout',
            'wp_favorites_layout_section'
        );
    }
    
    /**
     * Section callbacks
     */
    public function general_section_callback() {
        echo '<p>' . __('Configure general favorites functionality.', 'wp-favorites') . '</p>';
    }
    
    public function appearance_section_callback() {
        echo '<p>' . __('Customize the appearance of favorites icons and buttons.', 'wp-favorites') . '</p>';
    }
    
    public function layout_section_callback() {
        echo '<p>' . __('Configure the layout of the favorites page.', 'wp-favorites') . '</p>';
    }
    
    /**
     * Field callbacks
     */
    public function favorites_page_field() {
        $page_id = get_option('wp_favorites_favorites_page_id', 0);
        $pages = get_pages();
        
        echo '<select name="wp_favorites_favorites_page_id" id="wp_favorites_favorites_page_id">';
        echo '<option value="0">' . __('Select a page...', 'wp-favorites') . '</option>';
        
        foreach ($pages as $page) {
            $selected = selected($page_id, $page->ID, false);
            echo '<option value="' . esc_attr($page->ID) . '" ' . $selected . '>';
            echo esc_html($page->post_title);
            echo '</option>';
        }
        
        echo '</select>';
        echo '<p class="description">' . __('Select the page where favorites will be displayed. Add the shortcode [wp_favorites_list] to the page content.', 'wp-favorites') . '</p>';
    }
    
    public function show_on_shop_field() {
        $value = get_option('wp_favorites_show_on_shop', 'yes');
        echo '<label>';
        echo '<input type="checkbox" name="wp_favorites_show_on_shop" value="yes" ' . checked($value, 'yes', false) . '>';
        echo ' ' . __('Show favorites icon on shop page products', 'wp-favorites');
        echo '</label>';
    }
    
    public function show_on_category_field() {
        $value = get_option('wp_favorites_show_on_category', 'yes');
        echo '<label>';
        echo '<input type="checkbox" name="wp_favorites_show_on_category" value="yes" ' . checked($value, 'yes', false) . '>';
        echo ' ' . __('Show favorites icon on category page products', 'wp-favorites');
        echo '</label>';
    }
    
    public function show_on_single_field() {
        $value = get_option('wp_favorites_show_on_single', 'yes');
        echo '<label>';
        echo '<input type="checkbox" name="wp_favorites_show_on_single" value="yes" ' . checked($value, 'yes', false) . '>';
        echo ' ' . __('Show favorites icon on single product pages', 'wp-favorites');
        echo '</label>';
    }
    
    public function icon_position_field() {
        $value = get_option('wp_favorites_icon_position', 'top-right');
        $positions = array(
            'top-left' => __('Top Left', 'wp-favorites'),
            'top-right' => __('Top Right', 'wp-favorites'),
            'bottom-left' => __('Bottom Left', 'wp-favorites'),
            'bottom-right' => __('Bottom Right', 'wp-favorites')
        );
        
        echo '<select name="wp_favorites_icon_position" id="wp_favorites_icon_position">';
        foreach ($positions as $key => $label) {
            $selected = selected($value, $key, false);
            echo '<option value="' . esc_attr($key) . '" ' . $selected . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
    }
    
    public function icon_size_field() {
        $value = get_option('wp_favorites_icon_size', 'medium');
        $sizes = array(
            'small' => __('Small (30px)', 'wp-favorites'),
            'medium' => __('Medium (40px)', 'wp-favorites'),
            'large' => __('Large (50px)', 'wp-favorites')
        );
        
        echo '<select name="wp_favorites_icon_size" id="wp_favorites_icon_size">';
        foreach ($sizes as $key => $label) {
            $selected = selected($value, $key, false);
            echo '<option value="' . esc_attr($key) . '" ' . $selected . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
    }
    
    public function icon_colors_field() {
        $color = get_option('wp_favorites_icon_color', '#666666');
        $color_active = get_option('wp_favorites_icon_color_active', '#e74c3c');
        
        echo '<div class="wp-favorites-color-fields">';
        echo '<div class="color-field">';
        echo '<label for="wp_favorites_icon_color">' . __('Default Color:', 'wp-favorites') . '</label>';
        echo '<input type="color" name="wp_favorites_icon_color" id="wp_favorites_icon_color" value="' . esc_attr($color) . '">';
        echo '</div>';
        
        echo '<div class="color-field">';
        echo '<label for="wp_favorites_icon_color_active">' . __('Active Color:', 'wp-favorites') . '</label>';
        echo '<input type="color" name="wp_favorites_icon_color_active" id="wp_favorites_icon_color_active" value="' . esc_attr($color_active) . '">';
        echo '</div>';
        echo '</div>';
    }
    
    public function custom_icon_field() {
        $icon_id = get_option('wp_favorites_custom_icon_id', 0);
        $icon_url = $icon_id ? wp_get_attachment_url($icon_id) : '';
        
        echo '<div class="wp-favorites-custom-icon-field">';
        echo '<div class="icon-preview">';
        if ($icon_url) {
            echo '<img src="' . esc_url($icon_url) . '" alt="' . __('Custom Icon', 'wp-favorites') . '" style="max-width: 50px; max-height: 50px;">';
        } else {
            echo '<div class="no-icon">' . __('No custom icon selected', 'wp-favorites') . '</div>';
        }
        echo '</div>';
        
        echo '<input type="hidden" name="wp_favorites_custom_icon_id" id="wp_favorites_custom_icon_id" value="' . esc_attr($icon_id) . '">';
        echo '<button type="button" class="button wp-favorites-upload-icon">' . __('Upload Icon', 'wp-favorites') . '</button>';
        
        if ($icon_id) {
            echo '<button type="button" class="button wp-favorites-remove-icon">' . __('Remove Icon', 'wp-favorites') . '</button>';
        }
        
        echo '<p class="description">' . __('Upload a custom icon for favorites. Recommended size: 24x24px. Supported formats: SVG, PNG, JPG.', 'wp-favorites') . '</p>';
        echo '</div>';
    }
    
    public function grid_columns_field() {
        $desktop = get_option('wp_favorites_grid_columns_desktop', 4);
        $tablet = get_option('wp_favorites_grid_columns_tablet', 3);
        $mobile = get_option('wp_favorites_grid_columns_mobile', 2);
        
        echo '<div class="wp-favorites-grid-columns">';
        
        echo '<div class="column-field">';
        echo '<label for="wp_favorites_grid_columns_desktop">' . __('Desktop:', 'wp-favorites') . '</label>';
        echo '<select name="wp_favorites_grid_columns_desktop" id="wp_favorites_grid_columns_desktop">';
        for ($i = 1; $i <= 6; $i++) {
            $selected = selected($desktop, $i, false);
            echo '<option value="' . $i . '" ' . $selected . '>' . $i . '</option>';
        }
        echo '</select>';
        echo '</div>';
        
        echo '<div class="column-field">';
        echo '<label for="wp_favorites_grid_columns_tablet">' . __('Tablet:', 'wp-favorites') . '</label>';
        echo '<select name="wp_favorites_grid_columns_tablet" id="wp_favorites_grid_columns_tablet">';
        for ($i = 1; $i <= 4; $i++) {
            $selected = selected($tablet, $i, false);
            echo '<option value="' . $i . '" ' . $selected . '>' . $i . '</option>';
        }
        echo '</select>';
        echo '</div>';
        
        echo '<div class="column-field">';
        echo '<label for="wp_favorites_grid_columns_mobile">' . __('Mobile:', 'wp-favorites') . '</label>';
        echo '<select name="wp_favorites_grid_columns_mobile" id="wp_favorites_grid_columns_mobile">';
        for ($i = 1; $i <= 3; $i++) {
            $selected = selected($mobile, $i, false);
            echo '<option value="' . $i . '" ' . $selected . '>' . $i . '</option>';
        }
        echo '</select>';
        echo '</div>';
        
        echo '</div>';
    }
    
    /**
     * Render admin page
     */
    public function render_admin_page() {
        if (isset($_GET['settings-updated'])) {
            add_settings_error('wp_favorites_messages', 'wp_favorites_message', __('Settings saved successfully.', 'wp-favorites'), 'updated');
        }
        
        settings_errors('wp_favorites_messages');
        
        $active_tab = isset($_GET['tab']) ? $_GET['tab'] : 'general';
        ?>
        <div class="wrap">
            <h1><?php _e('WP Favorites Settings', 'wp-favorites'); ?></h1>
            
            <nav class="nav-tab-wrapper">
                <a href="?page=<?php echo $this->page_slug; ?>&tab=general"
                   class="nav-tab <?php echo $active_tab === 'general' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('General', 'wp-favorites'); ?>
                </a>
                <a href="?page=<?php echo $this->page_slug; ?>&tab=appearance"
                   class="nav-tab <?php echo $active_tab === 'appearance' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Appearance', 'wp-favorites'); ?>
                </a>
                <a href="?page=<?php echo $this->page_slug; ?>&tab=layout"
                   class="nav-tab <?php echo $active_tab === 'layout' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Layout', 'wp-favorites'); ?>
                </a>
                <a href="?page=<?php echo $this->page_slug; ?>&tab=translations"
                   class="nav-tab <?php echo $active_tab === 'translations' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Translations', 'wp-favorites'); ?>
                </a>
            </nav>
            
            <?php if ($active_tab === 'translations'): ?>
                <?php $this->render_translations_tab(); ?>
            <?php else: ?>
            <form method="post" action="options.php">
                <?php
                if ($active_tab === 'general') {
                    settings_fields('wp_favorites_general');
                    do_settings_sections('wp_favorites_general');
                } elseif ($active_tab === 'appearance') {
                    settings_fields('wp_favorites_appearance');
                    do_settings_sections('wp_favorites_appearance');
                } elseif ($active_tab === 'layout') {
                    settings_fields('wp_favorites_layout');
                    do_settings_sections('wp_favorites_layout');
                }

                submit_button();
                ?>
            </form>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if ('settings_page_' . $this->page_slug !== $hook) {
            return;
        }
        
        wp_enqueue_media();
        
        wp_enqueue_style(
            'wp-favorites-admin',
            WP_FAVORITES_PLUGIN_URL . 'admin/css/admin-style.css',
            array(),
            WP_FAVORITES_VERSION
        );
        
        wp_enqueue_script(
            'wp-favorites-admin',
            WP_FAVORITES_PLUGIN_URL . 'admin/js/admin-script.js',
            array('jquery', 'media-upload'),
            WP_FAVORITES_VERSION,
            true
        );
        
        wp_localize_script('wp-favorites-admin', 'wpFavoritesAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wp_favorites_admin_nonce'),
            'strings' => array(
                'selectIcon' => __('Select Icon', 'wp-favorites'),
                'useIcon' => __('Use This Icon', 'wp-favorites'),
                'confirmRemove' => __('Are you sure you want to remove this icon?', 'wp-favorites'),
                'translationRequired' => __('Translation is required.', 'wp-favorites'),
                'confirmDelete' => __('Are you sure you want to delete this translation?', 'wp-favorites'),
                'savingTranslation' => __('Saving translation...', 'wp-favorites'),
                'translationSaved' => __('Translation saved successfully.', 'wp-favorites'),
                'translationDeleted' => __('Translation deleted successfully.', 'wp-favorites'),
                'errorSaving' => __('An error occurred while saving the translation.', 'wp-favorites'),
                'errorDeleting' => __('An error occurred while deleting the translation.', 'wp-favorites'),
                'invalidFile' => __('Invalid JSON file format.', 'wp-favorites'),
                'exportError' => __('An error occurred while exporting translations.', 'wp-favorites'),
                'importError' => __('An error occurred while importing translations.', 'wp-favorites')
            )
        ));
    }
    
    /**
     * Handle icon upload
     */
    public function handle_icon_upload() {
        if (!wp_verify_nonce($_POST['nonce'], 'wp_favorites_admin_nonce')) {
            wp_send_json_error(__('Security check failed.', 'wp-favorites'));
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'wp-favorites'));
        }
        
        $attachment_id = intval($_POST['attachment_id']);
        
        if (!$attachment_id) {
            wp_send_json_error(__('Invalid attachment ID.', 'wp-favorites'));
        }
        
        // Verify it's an image
        if (!wp_attachment_is_image($attachment_id)) {
            wp_send_json_error(__('Please select a valid image file.', 'wp-favorites'));
        }
        
        // Update option
        update_option('wp_favorites_custom_icon_id', $attachment_id);
        
        $icon_url = wp_get_attachment_url($attachment_id);
        
        wp_send_json_success(array(
            'icon_url' => $icon_url,
            'message' => __('Icon uploaded successfully.', 'wp-favorites')
        ));
    }
    
    /**
     * Add settings link to plugins page
     */
    public function add_settings_link($links) {
        $settings_link = '<a href="' . admin_url('options-general.php?page=' . $this->page_slug) . '">' . __('Settings', 'wp-favorites') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }
    
    /**
     * Admin notices
     */
    public function admin_notices() {
        // Check if favorites page is set
        $page_id = get_option('wp_favorites_favorites_page_id', 0);
        if (!$page_id && isset($_GET['page']) && $_GET['page'] === $this->page_slug) {
            echo '<div class="notice notice-warning">';
            echo '<p>' . sprintf(
                __('Please select a page for displaying favorites in the %s.', 'wp-favorites'),
                '<a href="' . admin_url('options-general.php?page=' . $this->page_slug) . '">' . __('settings', 'wp-favorites') . '</a>'
            ) . '</p>';
            echo '</div>';
        }
    }

    /**
     * Render translations tab
     */
    public function render_translations_tab() {
        $translations_manager = WP_Favorites_Translations::get_instance();
        $available_languages = $translations_manager->get_available_languages();
        $current_language = isset($_GET['lang']) ? sanitize_text_field($_GET['lang']) : get_locale();
        $translatable_strings = $translations_manager->get_all_translatable_strings();
        $custom_translations = $translations_manager->get_translations_by_language($current_language);

        // Create array for easier lookup
        $custom_translations_lookup = array();
        foreach ($custom_translations as $translation) {
            $key = $translation->string_key;
            $custom_translations_lookup[$key] = $translation;
        }
        ?>
        <div class="wp-favorites-translations-tab">
            <div class="translations-header">
                <div class="language-selector">
                    <label for="translation-language"><?php _e('Select Language:', 'wp-favorites'); ?></label>
                    <select id="translation-language" onchange="window.location.href='?page=<?php echo $this->page_slug; ?>&tab=translations&lang=' + this.value">
                        <?php foreach ($available_languages as $code => $name): ?>
                            <option value="<?php echo esc_attr($code); ?>" <?php selected($current_language, $code); ?>>
                                <?php echo esc_html($name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="translation-actions">
                    <button type="button" class="button" id="export-translations">
                        <?php _e('Export Translations', 'wp-favorites'); ?>
                    </button>
                    <button type="button" class="button" id="import-translations">
                        <?php _e('Import Translations', 'wp-favorites'); ?>
                    </button>
                    <input type="file" id="import-file" accept=".json" style="display: none;">
                </div>
            </div>

            <div class="translations-content">
                <div class="translations-stats">
                    <p>
                        <?php
                        $total_strings = count($translatable_strings);
                        $translated_strings = count($custom_translations);
                        $percentage = $total_strings > 0 ? round(($translated_strings / $total_strings) * 100) : 0;

                        echo sprintf(
                            __('Translation Progress: %d%% (%d of %d strings translated)', 'wp-favorites'),
                            $percentage,
                            $translated_strings,
                            $total_strings
                        );
                        ?>
                    </p>
                </div>

                <div class="translations-table-container">
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th style="width: 40%;"><?php _e('Original Text', 'wp-favorites'); ?></th>
                                <th style="width: 40%;"><?php _e('Translation', 'wp-favorites'); ?></th>
                                <th style="width: 20%;"><?php _e('Actions', 'wp-favorites'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($translatable_strings as $string):
                                $string_key = $translations_manager->generate_string_key($string);
                                $custom_translation = isset($custom_translations_lookup[$string_key]) ? $custom_translations_lookup[$string_key] : null;
                                $translated_text = $custom_translation ? $custom_translation->translated_text : '';
                                $row_class = $custom_translation ? 'translated' : 'untranslated';
                            ?>
                            <tr class="translation-row <?php echo $row_class; ?>" data-string-key="<?php echo esc_attr($string_key); ?>">
                                <td class="original-text">
                                    <div class="text-content"><?php echo esc_html($string); ?></div>
                                </td>
                                <td class="translation-input">
                                    <textarea
                                        class="translation-textarea"
                                        data-original="<?php echo esc_attr($string); ?>"
                                        data-language="<?php echo esc_attr($current_language); ?>"
                                        placeholder="<?php _e('Enter translation...', 'wp-favorites'); ?>"
                                    ><?php echo esc_textarea($translated_text); ?></textarea>
                                </td>
                                <td class="translation-actions">
                                    <button type="button" class="button button-primary save-translation">
                                        <?php _e('Save', 'wp-favorites'); ?>
                                    </button>
                                    <?php if ($custom_translation): ?>
                                    <button type="button" class="button delete-translation">
                                        <?php _e('Delete', 'wp-favorites'); ?>
                                    </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php
    }
}
