<?php
/**
 * WP Favorites Core Class
 * 
 * Handles core functionality for the favorites system
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Core {
    
    /**
     * Single instance
     */
    private static $instance = null;
    
    /**
     * Table name
     */
    private $table_name;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'wp_favorites';
        
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add favorites functionality to products
        add_action('woocommerce_before_shop_loop_item', array($this, 'add_favorites_button_to_loop'), 15);
        add_action('woocommerce_before_single_product_summary', array($this, 'add_favorites_button_to_single'), 25);
        
        // Handle user login/logout for favorites sync
        add_action('wp_login', array($this, 'sync_guest_favorites'), 10, 2);
        add_action('wp_logout', array($this, 'clear_user_session'));
        
        // Add shortcode for favorites page
        add_shortcode('wp_favorites_list', array($this, 'favorites_list_shortcode'));
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Add favorites button to shop loop
     */
    public function add_favorites_button_to_loop() {
        if (get_option('wp_favorites_show_on_shop') === 'yes') {
            $this->render_favorites_button();
        }
    }
    
    /**
     * Add favorites button to single product
     */
    public function add_favorites_button_to_single() {
        if (get_option('wp_favorites_show_on_single') === 'yes') {
            $this->render_favorites_button();
        }
    }
    
    /**
     * Render favorites button
     */
    public function render_favorites_button() {
        global $product;
        
        if (!$product || !is_a($product, 'WC_Product')) {
            return;
        }
        
        $product_id = $product->get_id();
        $is_favorite = $this->is_favorite($product_id);
        $icon_class = $is_favorite ? 'wp-favorites-icon active' : 'wp-favorites-icon';
        $title = $is_favorite ? __('Remove from favorites', 'wp-favorites') : __('Add to favorites', 'wp-favorites');
        
        ?>
        <div class="wp-favorites-wrapper" data-product-id="<?php echo esc_attr($product_id); ?>">
            <button class="<?php echo esc_attr($icon_class); ?>" 
                    title="<?php echo esc_attr($title); ?>"
                    aria-label="<?php echo esc_attr($title); ?>">
                <?php echo $this->get_favorites_icon($is_favorite); ?>
            </button>
        </div>
        <?php
    }
    
    /**
     * Get favorites icon HTML
     */
    public function get_favorites_icon($is_active = false) {
        $custom_icon_id = get_option('wp_favorites_custom_icon_id', 0);
        
        if ($custom_icon_id && $custom_icon_url = wp_get_attachment_url($custom_icon_id)) {
            return sprintf(
                '<img src="%s" alt="%s" class="wp-favorites-custom-icon %s">',
                esc_url($custom_icon_url),
                esc_attr__('Favorite', 'wp-favorites'),
                $is_active ? 'active' : ''
            );
        }
        
        // Default heart icon (SVG)
        $heart_class = $is_active ? 'filled' : 'empty';
        return sprintf(
            '<svg class="wp-favorites-heart %s" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </svg>',
            esc_attr($heart_class)
        );
    }
    
    /**
     * Check if product is in favorites
     */
    public function is_favorite($product_id) {
        $user_id = get_current_user_id();
        
        if ($user_id) {
            // Logged in user - check database
            global $wpdb;
            $count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->table_name} WHERE user_id = %d AND product_id = %d",
                $user_id,
                $product_id
            ));
            return $count > 0;
        } else {
            // Guest user - check session/cookie
            $guest_favorites = $this->get_guest_favorites();
            return in_array($product_id, $guest_favorites);
        }
    }
    
    /**
     * Add product to favorites
     */
    public function add_favorite($product_id) {
        $user_id = get_current_user_id();
        
        if ($user_id) {
            // Logged in user - add to database
            global $wpdb;
            
            $result = $wpdb->insert(
                $this->table_name,
                array(
                    'user_id' => $user_id,
                    'product_id' => $product_id,
                    'date_added' => current_time('mysql')
                ),
                array('%d', '%d', '%s')
            );
            
            return $result !== false;
        } else {
            // Guest user - add to session
            $guest_favorites = $this->get_guest_favorites();
            if (!in_array($product_id, $guest_favorites)) {
                $guest_favorites[] = $product_id;
                $this->set_guest_favorites($guest_favorites);
            }
            return true;
        }
    }
    
    /**
     * Remove product from favorites
     */
    public function remove_favorite($product_id) {
        $user_id = get_current_user_id();
        
        if ($user_id) {
            // Logged in user - remove from database
            global $wpdb;
            
            $result = $wpdb->delete(
                $this->table_name,
                array(
                    'user_id' => $user_id,
                    'product_id' => $product_id
                ),
                array('%d', '%d')
            );
            
            return $result !== false;
        } else {
            // Guest user - remove from session
            $guest_favorites = $this->get_guest_favorites();
            $key = array_search($product_id, $guest_favorites);
            if ($key !== false) {
                unset($guest_favorites[$key]);
                $this->set_guest_favorites(array_values($guest_favorites));
            }
            return true;
        }
    }
    
    /**
     * Get user favorites
     */
    public function get_user_favorites($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if ($user_id) {
            // Logged in user
            global $wpdb;
            $favorites = $wpdb->get_col($wpdb->prepare(
                "SELECT product_id FROM {$this->table_name} WHERE user_id = %d ORDER BY date_added DESC",
                $user_id
            ));
            return array_map('intval', $favorites);
        } else {
            // Guest user
            return $this->get_guest_favorites();
        }
    }
    
    /**
     * Get guest favorites from session/cookie
     */
    private function get_guest_favorites() {
        if (!session_id()) {
            session_start();
        }
        
        $favorites = isset($_SESSION['wp_favorites']) ? $_SESSION['wp_favorites'] : array();
        
        // Also check cookie as fallback
        if (empty($favorites) && isset($_COOKIE['wp_favorites'])) {
            $cookie_data = json_decode(stripslashes($_COOKIE['wp_favorites']), true);
            $favorites = is_array($cookie_data) ? $cookie_data : array();
        }
        
        return array_map('intval', $favorites);
    }
    
    /**
     * Set guest favorites in session/cookie
     */
    private function set_guest_favorites($favorites) {
        if (!session_id()) {
            session_start();
        }
        
        $_SESSION['wp_favorites'] = $favorites;
        
        // Also set cookie for persistence
        setcookie(
            'wp_favorites',
            json_encode($favorites),
            time() + (30 * DAY_IN_SECONDS), // 30 days
            COOKIEPATH,
            COOKIE_DOMAIN
        );
    }
    
    /**
     * Sync guest favorites when user logs in
     */
    public function sync_guest_favorites($user_login, $user) {
        $guest_favorites = $this->get_guest_favorites();
        
        if (!empty($guest_favorites)) {
            foreach ($guest_favorites as $product_id) {
                if (!$this->is_favorite($product_id)) {
                    $this->add_favorite($product_id);
                }
            }
            
            // Clear guest favorites after sync
            $this->clear_guest_favorites();
        }
    }
    
    /**
     * Clear guest favorites
     */
    private function clear_guest_favorites() {
        if (session_id()) {
            unset($_SESSION['wp_favorites']);
        }
        
        setcookie(
            'wp_favorites',
            '',
            time() - 3600,
            COOKIEPATH,
            COOKIE_DOMAIN
        );
    }
    
    /**
     * Clear user session on logout
     */
    public function clear_user_session() {
        $this->clear_guest_favorites();
    }
    
    /**
     * Favorites list shortcode
     */
    public function favorites_list_shortcode($atts) {
        $atts = shortcode_atts(array(
            'columns' => get_option('wp_favorites_grid_columns_desktop', 4),
            'show_remove' => 'yes'
        ), $atts);
        
        ob_start();
        $this->render_favorites_list($atts);
        return ob_get_clean();
    }
    
    /**
     * Render favorites list
     */
    public function render_favorites_list($args = array()) {
        $favorites = $this->get_user_favorites();
        
        if (empty($favorites)) {
            echo '<div class="wp-favorites-empty">';
            echo '<p>' . __('Your favorites list is empty.', 'wp-favorites') . '</p>';
            echo '<a href="' . esc_url(wc_get_page_permalink('shop')) . '" class="button">';
            echo __('Continue Shopping', 'wp-favorites');
            echo '</a>';
            echo '</div>';
            return;
        }
        
        $columns = isset($args['columns']) ? intval($args['columns']) : 4;
        $show_remove = isset($args['show_remove']) ? $args['show_remove'] : 'yes';
        
        echo '<div class="wp-favorites-grid" data-columns="' . esc_attr($columns) . '">';
        
        foreach ($favorites as $product_id) {
            $product = wc_get_product($product_id);
            if (!$product) continue;
            
            $this->render_favorite_item($product, $show_remove === 'yes');
        }
        
        echo '</div>';
    }
    
    /**
     * Render single favorite item
     */
    private function render_favorite_item($product, $show_remove = true) {
        ?>
        <div class="wp-favorites-item" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
            <div class="wp-favorites-item-image">
                <a href="<?php echo esc_url($product->get_permalink()); ?>">
                    <?php echo $product->get_image('woocommerce_thumbnail'); ?>
                </a>
                <?php if ($show_remove): ?>
                <button class="wp-favorites-remove" title="<?php esc_attr_e('Remove from favorites', 'wp-favorites'); ?>">
                    <span>&times;</span>
                </button>
                <?php endif; ?>
            </div>
            <div class="wp-favorites-item-content">
                <h3 class="wp-favorites-item-title">
                    <a href="<?php echo esc_url($product->get_permalink()); ?>">
                        <?php echo esc_html($product->get_name()); ?>
                    </a>
                </h3>
                <div class="wp-favorites-item-price">
                    <?php echo $product->get_price_html(); ?>
                </div>
                <div class="wp-favorites-item-actions">
                    <?php woocommerce_template_loop_add_to_cart(); ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Enqueue styles
        wp_enqueue_style(
            'wp-favorites-frontend',
            WP_FAVORITES_PLUGIN_URL . 'assets/css/frontend-style.css',
            array(),
            WP_FAVORITES_VERSION
        );
        
        // Enqueue scripts
        wp_enqueue_script(
            'wp-favorites-frontend',
            WP_FAVORITES_PLUGIN_URL . 'assets/js/frontend-script.js',
            array('jquery'),
            WP_FAVORITES_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('wp-favorites-frontend', 'wpFavorites', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wp_favorites_nonce'),
            'strings' => array(
                'addToFavorites' => __('Add to favorites', 'wp-favorites'),
                'removeFromFavorites' => __('Remove from favorites', 'wp-favorites'),
                'error' => __('An error occurred. Please try again.', 'wp-favorites'),
                'confirmRemove' => __('Are you sure you want to remove this item from your favorites?', 'wp-favorites')
            )
        ));
    }
}
