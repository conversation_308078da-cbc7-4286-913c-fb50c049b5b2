/**
 * WP Favorites Frontend Styles
 */

/* Favorites Button Wrapper */
.wp-favorites-wrapper {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}

/* Favorites Icon Button */
.wp-favorites-icon {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.wp-favorites-icon:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.wp-favorites-icon:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* Heart Icon SVG */
.wp-favorites-heart {
    width: 20px;
    height: 20px;
    transition: all 0.3s ease;
}

.wp-favorites-heart.empty {
    fill: none;
    stroke: #666;
    stroke-width: 2;
}

.wp-favorites-heart.filled {
    fill: #e74c3c;
    stroke: #e74c3c;
    stroke-width: 1;
}

.wp-favorites-icon:hover .wp-favorites-heart.empty {
    stroke: #e74c3c;
}

.wp-favorites-icon:hover .wp-favorites-heart.filled {
    fill: #c0392b;
    stroke: #c0392b;
}

/* Custom Icon */
.wp-favorites-custom-icon {
    width: 20px;
    height: 20px;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.wp-favorites-custom-icon.active {
    opacity: 1;
}

.wp-favorites-icon:hover .wp-favorites-custom-icon {
    opacity: 1;
    transform: scale(1.1);
}

/* Animation for adding/removing */
.wp-favorites-icon.adding {
    animation: favoritesPulse 0.6s ease;
}

@keyframes favoritesPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Favorites Grid */
.wp-favorites-grid {
    display: grid;
    gap: 20px;
    margin: 20px 0;
}

.wp-favorites-grid[data-columns="1"] {
    grid-template-columns: 1fr;
}

.wp-favorites-grid[data-columns="2"] {
    grid-template-columns: repeat(2, 1fr);
}

.wp-favorites-grid[data-columns="3"] {
    grid-template-columns: repeat(3, 1fr);
}

.wp-favorites-grid[data-columns="4"] {
    grid-template-columns: repeat(4, 1fr);
}

/* Favorites Item */
.wp-favorites-item {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.wp-favorites-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Favorites Item Image */
.wp-favorites-item-image {
    position: relative;
    overflow: hidden;
}

.wp-favorites-item-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.wp-favorites-item:hover .wp-favorites-item-image img {
    transform: scale(1.05);
}

/* Remove Button */
.wp-favorites-remove {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(231, 76, 60, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
    transition: all 0.3s ease;
    opacity: 0;
    transform: scale(0.8);
}

.wp-favorites-item:hover .wp-favorites-remove {
    opacity: 1;
    transform: scale(1);
}

.wp-favorites-remove:hover {
    background: rgba(192, 57, 43, 1);
    transform: scale(1.1);
}

/* Favorites Item Content */
.wp-favorites-item-content {
    padding: 15px;
}

.wp-favorites-item-title {
    margin: 0 0 10px 0;
    font-size: 16px;
    line-height: 1.4;
}

.wp-favorites-item-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.wp-favorites-item-title a:hover {
    color: #007cba;
}

.wp-favorites-item-price {
    margin: 0 0 15px 0;
    font-weight: bold;
    color: #e74c3c;
}

.wp-favorites-item-actions {
    margin-top: 15px;
}

.wp-favorites-item-actions .button {
    width: 100%;
    text-align: center;
}

/* Empty Favorites */
.wp-favorites-empty {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.wp-favorites-empty-icon {
    margin: 0 0 20px 0;
}

.wp-favorites-empty-icon svg {
    width: 80px;
    height: 80px;
    fill: #ddd;
}

.wp-favorites-empty h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 24px;
}

.wp-favorites-empty p {
    margin: 0 0 30px 0;
    font-size: 16px;
    line-height: 1.6;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.wp-favorites-shop-button {
    display: inline-block;
    padding: 12px 30px;
    background: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.wp-favorites-shop-button:hover {
    background: #005a87;
    color: white;
}

/* Pagination */
.wp-favorites-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 30px 0;
}

.wp-favorites-page-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #007cba;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    min-width: 40px;
    text-align: center;
}

.wp-favorites-page-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.wp-favorites-page-btn.current {
    background: #007cba;
    color: white;
    border-color: #007cba;
}

.wp-favorites-page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Loading State */
.wp-favorites-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.wp-favorites-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: favoritesSpinner 1s linear infinite;
}

@keyframes favoritesSpinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .wp-favorites-grid[data-columns="4"] {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .wp-favorites-grid[data-columns="3"] {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .wp-favorites-wrapper {
        top: 5px;
        right: 5px;
    }
    
    .wp-favorites-icon {
        width: 35px;
        height: 35px;
    }
    
    .wp-favorites-heart {
        width: 18px;
        height: 18px;
    }
    
    .wp-favorites-custom-icon {
        width: 18px;
        height: 18px;
    }
    
    .wp-favorites-item-content {
        padding: 12px;
    }
    
    .wp-favorites-item-title {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .wp-favorites-grid[data-columns="4"],
    .wp-favorites-grid[data-columns="3"],
    .wp-favorites-grid[data-columns="2"] {
        grid-template-columns: 1fr;
    }
    
    .wp-favorites-grid {
        gap: 15px;
    }
    
    .wp-favorites-pagination {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .wp-favorites-page-btn {
        padding: 6px 10px;
        min-width: 35px;
        font-size: 14px;
    }
}

/* RTL Support */
[dir="rtl"] .wp-favorites-wrapper {
    right: auto;
    left: 10px;
}

[dir="rtl"] .wp-favorites-remove {
    right: auto;
    left: 10px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .wp-favorites-icon {
        background: #fff;
        border: 2px solid #000;
    }
    
    .wp-favorites-heart.empty {
        stroke: #000;
    }
    
    .wp-favorites-heart.filled {
        fill: #000;
        stroke: #000;
    }
    
    .wp-favorites-item {
        border: 2px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .wp-favorites-icon,
    .wp-favorites-heart,
    .wp-favorites-custom-icon,
    .wp-favorites-item,
    .wp-favorites-item-image img,
    .wp-favorites-remove,
    .wp-favorites-page-btn {
        transition: none;
    }
    
    .wp-favorites-icon.adding {
        animation: none;
    }
    
    @keyframes favoritesPulse {
        0%, 100% { transform: scale(1); }
    }
}
