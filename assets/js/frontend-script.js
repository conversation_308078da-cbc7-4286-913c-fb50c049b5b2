/**
 * WP Favorites Frontend JavaScript
 */

(function($) {
    'use strict';

    // Main WP Favorites object
    window.WPFavorites = {
        
        // Initialize
        init: function() {
            this.bindEvents();
            this.updateFavoritesCount();
        },
        
        // Bind events
        bindEvents: function() {
            // Toggle favorite on icon click
            $(document).on('click', '.wp-favorites-icon', this.toggleFavorite);
            
            // Remove favorite from list
            $(document).on('click', '.wp-favorites-remove', this.removeFavorite);
            
            // Pagination
            $(document).on('click', '.wp-favorites-page-btn', this.loadPage);
            
            // Handle AJAX cart additions
            $(document.body).on('added_to_cart', this.handleCartAdd);
        },
        
        // Toggle favorite status
        toggleFavorite: function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $button = $(this);
            var $wrapper = $button.closest('.wp-favorites-wrapper');
            var productId = $wrapper.data('product-id');
            
            if (!productId) {
                console.error('WP Favorites: Product ID not found');
                return;
            }
            
            // Prevent multiple clicks
            if ($button.hasClass('wp-favorites-loading')) {
                return;
            }
            
            // Add loading state
            $button.addClass('wp-favorites-loading');
            
            // AJAX request
            $.ajax({
                url: wpFavorites.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_toggle',
                    product_id: productId,
                    nonce: wpFavorites.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WPFavorites.handleToggleSuccess($button, response.data);
                    } else {
                        WPFavorites.showError(response.data.message || wpFavorites.strings.error);
                    }
                },
                error: function() {
                    WPFavorites.showError(wpFavorites.strings.error);
                },
                complete: function() {
                    $button.removeClass('wp-favorites-loading');
                }
            });
        },
        
        // Handle successful toggle
        handleToggleSuccess: function($button, data) {
            // Update button state
            if (data.is_favorite) {
                $button.addClass('active');
            } else {
                $button.removeClass('active');
            }
            
            // Update button title
            $button.attr('title', data.button_title);
            $button.attr('aria-label', data.button_title);
            
            // Update icon HTML
            $button.html(data.icon_html);
            
            // Add animation
            $button.addClass('adding');
            setTimeout(function() {
                $button.removeClass('adding');
            }, 600);
            
            // Update favorites count
            this.updateFavoritesCount(data.favorites_count);
            
            // Show success message
            this.showMessage(data.message, 'success');
            
            // Trigger custom event
            $(document).trigger('wp_favorites_toggled', {
                productId: $button.closest('.wp-favorites-wrapper').data('product-id'),
                isFavorite: data.is_favorite,
                count: data.favorites_count
            });
        },
        
        // Remove favorite from list
        removeFavorite: function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $button = $(this);
            var $item = $button.closest('.wp-favorites-item');
            var productId = $item.data('product-id');
            
            if (!productId) {
                console.error('WP Favorites: Product ID not found');
                return;
            }
            
            // Confirm removal
            if (!confirm(wpFavorites.strings.confirmRemove)) {
                return;
            }
            
            // Prevent multiple clicks
            if ($item.hasClass('wp-favorites-loading')) {
                return;
            }
            
            // Add loading state
            $item.addClass('wp-favorites-loading');
            
            // AJAX request
            $.ajax({
                url: wpFavorites.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_remove',
                    product_id: productId,
                    nonce: wpFavorites.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WPFavorites.handleRemoveSuccess($item, response.data);
                    } else {
                        WPFavorites.showError(response.data.message || wpFavorites.strings.error);
                        $item.removeClass('wp-favorites-loading');
                    }
                },
                error: function() {
                    WPFavorites.showError(wpFavorites.strings.error);
                    $item.removeClass('wp-favorites-loading');
                }
            });
        },
        
        // Handle successful removal
        handleRemoveSuccess: function($item, data) {
            // Animate removal
            $item.fadeOut(300, function() {
                $item.remove();
                
                // Check if list is empty
                var $grid = $('.wp-favorites-grid');
                if ($grid.find('.wp-favorites-item').length === 0) {
                    WPFavorites.showEmptyList();
                }
            });
            
            // Update favorites count
            this.updateFavoritesCount(data.favorites_count);
            
            // Show success message
            this.showMessage(data.message, 'success');
            
            // Update any favorite buttons for this product
            $('.wp-favorites-wrapper[data-product-id="' + $item.data('product-id') + '"] .wp-favorites-icon')
                .removeClass('active')
                .attr('title', wpFavorites.strings.addToFavorites)
                .attr('aria-label', wpFavorites.strings.addToFavorites);
            
            // Trigger custom event
            $(document).trigger('wp_favorites_removed', {
                productId: $item.data('product-id'),
                count: data.favorites_count
            });
        },
        
        // Load page (pagination)
        loadPage: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var page = $button.data('page');
            var $container = $('.wp-favorites-grid').parent();
            
            if (!page || $button.hasClass('current')) {
                return;
            }
            
            // Add loading state
            $container.addClass('wp-favorites-loading');
            
            // AJAX request
            $.ajax({
                url: wpFavorites.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_get_list',
                    page: page,
                    per_page: 12,
                    columns: $('.wp-favorites-grid').data('columns') || 4,
                    nonce: wpFavorites.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $container.html(response.data.html);
                        
                        // Scroll to top of list
                        $('html, body').animate({
                            scrollTop: $container.offset().top - 100
                        }, 300);
                    } else {
                        WPFavorites.showError(response.data.message || wpFavorites.strings.error);
                    }
                },
                error: function() {
                    WPFavorites.showError(wpFavorites.strings.error);
                },
                complete: function() {
                    $container.removeClass('wp-favorites-loading');
                }
            });
        },
        
        // Update favorites count
        updateFavoritesCount: function(count) {
            if (typeof count === 'undefined') {
                // Get count from server
                $.ajax({
                    url: wpFavorites.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'wp_favorites_get_count',
                        nonce: wpFavorites.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            WPFavorites.displayCount(response.data.count);
                        }
                    }
                });
            } else {
                this.displayCount(count);
            }
        },
        
        // Display count
        displayCount: function(count) {
            // Update any count displays
            $('.wp-favorites-count').text(count);
            
            // Update menu badges
            $('.wp-favorites-menu-badge').text(count).toggle(count > 0);
            
            // Trigger custom event
            $(document).trigger('wp_favorites_count_updated', { count: count });
        },
        
        // Show empty list
        showEmptyList: function() {
            var emptyHtml = '<div class="wp-favorites-empty">' +
                '<div class="wp-favorites-empty-icon">' +
                '<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">' +
                '<path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>' +
                '</svg>' +
                '</div>' +
                '<h3>' + wpFavorites.strings.emptyTitle + '</h3>' +
                '<p>' + wpFavorites.strings.emptyMessage + '</p>' +
                '<a href="' + wpFavorites.shopUrl + '" class="button wp-favorites-shop-button">' +
                wpFavorites.strings.continueShopping +
                '</a>' +
                '</div>';
            
            $('.wp-favorites-grid').parent().html(emptyHtml);
        },
        
        // Show message
        showMessage: function(message, type) {
            type = type || 'info';
            
            // Remove existing messages
            $('.wp-favorites-message').remove();
            
            // Create message element
            var $message = $('<div class="wp-favorites-message wp-favorites-message-' + type + '">' + message + '</div>');
            
            // Add to page
            if ($('.wp-favorites-grid').length) {
                $('.wp-favorites-grid').before($message);
            } else {
                $('body').prepend($message);
            }
            
            // Auto-hide after 3 seconds
            setTimeout(function() {
                $message.fadeOut(300, function() {
                    $message.remove();
                });
            }, 3000);
        },
        
        // Show error
        showError: function(message) {
            this.showMessage(message, 'error');
        },
        
        // Handle cart additions
        handleCartAdd: function(event, fragments, cart_hash, $button) {
            // This can be used to update favorites after cart additions
            // For example, to show a message or update UI
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        WPFavorites.init();
    });

    // Handle AJAX complete for WooCommerce
    $(document).ajaxComplete(function(event, xhr, settings) {
        // Re-initialize favorites buttons after AJAX content loads
        if (settings.url && settings.url.indexOf('wc-ajax') !== -1) {
            setTimeout(function() {
                // Re-bind events for new content
                WPFavorites.updateFavoritesCount();
            }, 100);
        }
    });

})(jQuery);
