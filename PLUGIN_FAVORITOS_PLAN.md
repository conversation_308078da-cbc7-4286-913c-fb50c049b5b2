# Favorites List Plugin - WooCommerce + Breakdance

## Plugin Structure

### Main Files
```
wp-favorites-plugin/
├── wp-favorites.php (main plugin file)
├── includes/
│   ├── class-favorites-core.php
│   ├── class-favorites-admin.php
│   ├── class-favorites-frontend.php
│   ├── class-favorites-ajax.php
│   └── class-favorites-breakdance.php
├── admin/
│   ├── css/admin-style.css
│   ├── js/admin-script.js
│   └── views/admin-page.php
├── assets/
│   ├── css/frontend-style.css
│   ├── js/frontend-script.js
│   └── icons/ (custom icons folder)
├── languages/
│   ├── wp-favorites-en_US.po
│   ├── wp-favorites-pt_PT.po
│   └── wp-favorites.pot (template)
└── templates/
    └── favorites-page.php
```

## Backend Features

### 1. Admin Panel
- **Settings page** in WP Admin
- **Custom icons upload** (.svg, .png, .jpg)
- **Favorites page selection** (dropdown with existing pages)
- **Style settings** (colors, sizes, positioning)
- **Full translation support** (all strings translatable)

### 2. Icon Management
- Multiple icon upload
- Uploaded icons preview
- Active icon selection
- Positioning: top right of products

### 3. Page Settings
- Choose page to display favorites
- Responsive cards layout
- Column options (desktop/tablet/mobile)

## Frontend Features

### 1. Product Icons
- Display on top right of each product
- States: empty/filled (favorited/not favorited)
- Click animation
- Works on: shop, category, single product

### 2. Favorites Page
- Responsive cards layout
- Adaptive grid (1-4 columns)
- Remove favorite button
- Empty list message

### 3. Breakdance Integration
- Custom "Favorites List" element
- Visual settings in builder
- Full Breakdance theme compatibility

## Internationalization (i18n)

### Translation Support
- **Text Domain**: `wp-favorites`
- **All strings wrapped** in translation functions
- **POT file generation** for translators
- **Default languages**: English, Portuguese
- **RTL support** ready

### Translatable Strings
- Admin interface labels
- Frontend messages
- Button texts
- Error/success messages
- Placeholder texts

### Translation Functions Used
```php
__('Text', 'wp-favorites')           // Simple translation
_e('Text', 'wp-favorites')           // Echo translation
_x('Text', 'context', 'wp-favorites') // Context translation
_n('Singular', 'Plural', $count, 'wp-favorites') // Plural
```

## Technologies

### Backend
- PHP 7.4+
- WordPress Hooks/Filters
- WooCommerce Actions
- AJAX for interactions
- **WordPress i18n API**

### Frontend
- JavaScript (ES6+)
- CSS Grid/Flexbox
- LocalStorage for persistence
- Responsive Design
- **Translatable JS strings**

### Breakdance
- Custom Elements API
- Builder Integration
- Dynamic Content

## Development Flow

### Phase 1: Core Plugin
1. Basic plugin structure
2. Activation/deactivation
3. Basic admin panel
4. **Translation setup**

### Phase 2: Backend Features
1. Icon upload system
2. Page settings
3. Style options
4. **Admin translations**

### Phase 3: Frontend Base
1. Product icon display
2. Favorites system (AJAX)
3. Listing page
4. **Frontend translations**

### Phase 4: Breakdance Integration
1. Custom element
2. Visual settings
3. Full compatibility

### Phase 5: Refinements
1. Responsiveness
2. Performance
3. **Translation completion**
4. Testing and optimizations

## Technical Considerations

- **Compatibility**: WordPress 5.0+, WooCommerce 4.0+
- **Performance**: Favorites cache, lazy loading
- **Security**: Nonces, sanitization, validation
- **SEO**: Semantic structure, meta tags
- **Accessibility**: ARIA labels, keyboard navigation
- **i18n**: Full translation support, RTL ready
- **Text Domain**: `wp-favorites` (consistent throughout)

## Translation Files Structure

### Required Files
- `wp-favorites.pot` - Translation template
- `wp-favorites-en_US.po/mo` - English (default)
- `wp-favorites-pt_PT.po/mo` - Portuguese
- Additional languages as needed

### Translation Workflow
1. Extract strings with WP-CLI or Poedit
2. Generate POT template
3. Create language-specific PO files
4. Compile to MO files for production
5. Load translations in plugin init